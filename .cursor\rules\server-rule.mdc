---
description: 
globs: 
alwaysApply: true
---
This is a SaaS system for small to medium distributors. These distributors have about: 1k customer stores, 50 routes, 300 items, and take about 1 order per store per week. We have a web dashboard, where the supplier can manage orders/invoices, items, and customer info, and an app, where the drivers can submit orders (DSD) and deliver orders. The stores are also able to access the app and only see their	order history and submit orders.

The dashboard uses nextjs with react and is deployed on vercel. The app uses expo and react native to deploy to iOS and android. Our server uses typescript and is deployed to heroku with a postgres add-on. We are using apollo and the apollo client for graphql between our systems. And using codegen to generate types on our server.

We are automatically creating invoices with every order (per a previous customer request), but only count the ones where the order is delivered in financial calculations. 

Right now we have the following tables, :
[
  {
    "table": "attain_user",
    "description": "Application users (stores) and account info",
    "columns": ["primary_route_id","id","name","email","store_id","buyer_id","user_name","address","created_at","created_by","password","timezone","supplier_beta","phone_number","approved","ein","driver","contact_email","service_type","store_group","route_id","qb_id","updated_at","config","archived"]
  },
  {
    "table": "brand_spotlight",
    "description": "Brands highlighted by a supplier for promotion",
    "columns": ["name","user_id","supplier_id","id","active","spotlight_image"]
  },
  {
    "table": "cart",
    "description": "Active shopping cart per user",
    "columns": ["csv","id","user_id","subtotal"]
  },
  {
    "table": "cart_item",
    "description": "Items contained in a cart",
    "columns": ["item_id","quantity","cart_id","id"]
  },
  {
    "table": "category",
    "description": "Product categories defined by a supplier",
    "columns": ["name","supplier_id","id","image"]
  },
  {
    "table": "category_item",
    "description": "Join table linking categories to items",
    "columns": ["category_id","item_id"]
  },
  {
    "table": "custom_item_price",
    "description": "User-specific price overrides for items",
    "columns": ["item_id","user_id","price"]
  },
  {
    "table": "employees",
    "description": "Supplier employees with dashboard or app access",
    "columns": ["name","phone","email","app_access","dashboard_access","created_at","updated_at","last_login","archived","id","supplier_id"]
  },
  {
    "table": "invoice",
    "description": "Invoices generated from orders",
    "columns": ["archived","user_id","date_created","date_due","subtotal","notes","discount","signature","signature_name","qb_id","updated_at","paid","payment_method","credit","config","route_id","id","date_received","total","order_id","invoice_id","supplier_id"]
  },
  {
    "table": "invoice_item",
    "description": "Line items on an invoice",
    "columns": ["is_mispick","checked_in_quantity","checked_in","price","upc2","upc1","size","name","invoice_id","id","cog_price","qb_id","item_id","unit_size","quantity"]
  },
  {
    "table": "item",
    "description": "Master catalog of products",
    "columns": ["supplier_code","id","name","nacs_category","nacs_subcategory","unit_size","upc1","upc2","price","image","correct_image","supplier","discounted_price","oos","upc3","upc4","outdated","size","local_item","metadata","qoh","brand","created_at","sorting_category","objectID","sorting_order","parent_brand","archived","updated_at","qb_id","qb_sync_token","qty_on_hand","cog_price","crv"]
  },
  {
    "table": "order_detail",
    "description": "Top-level customer orders",
    "columns": ["discount","notes","config","id","user_id","subtotal","status","date_submitted","single_supplier","order_name"]
  },
  {
    "table": "order_item",
    "description": "Items within an order",
    "columns": ["quantity","new_supplier","order_id","price_purchased_at","oos","id","item_id"]
  },
  {
    "table": "order_status",
    "description": "Key dates in an order’s lifecycle, we only use delivery_date, order_id, id, and supplier_id“,
    "columns": ["delivery_date","order_id","id","supplier_id","date_delivery","submission_date","delivering_date"]
  },
  {
    "table": "push_notification_tokens",
    "description": "Device tokens for push notifications on “app,
    "columns": ["token","user_id"]
  },
  {
    "table": "role_assignment",
    "description": "Employee-to-role mappings",
    "columns": ["role_id","assigned_at","employee_id"]
  },
  {
    "table": "roles",
    "description": "Definition of access roles",
    "columns": ["created_at","description","name","id","updated_at"]
  },
  {
    "table": "route",
    "description": "Delivery routes managed by suppliers",
    "columns": ["supplier_id","name","color","day_of_week","driver","config","id"]
  },
  {
    "table": "route_assignment",
    "description": "Employees assigned to delivery routes",
    "columns": ["route_id","id","updated_at","created_at","employee_id"]
  },
  {
    "table": "spotlight",
    "description": "User or supplier spotlight settings",
    "columns": ["id","active","user_id","supplier_id"]
  },
  {
    "table": "supplier",
    "description": "Supplier / distributor company records",
    "columns": ["minimum","need_signup","logo","spotlight_image","name","id","email","qb_refresh_token","qb_access_token","qb_realm_id","config","notification_email","notification_number","phone_number","address"]
  },
  {
    "table": "supplier_config",
    "description": "Key-value configuration per supplier",
    "columns": ["updated_at","value","key","supplier_id","id","created_at"]
  },
  {
    "table": "supplier_times",
    "description": "Delivery and cutoff scheduling for suppliers. We use this table for associating suppliers (supplier_id) to stores (business_id)“,
    "columns": ["days_to_delivery","business_id","delivery_time","cutoff_day","cutoff_time","supplier_id","id","delivery_day"]
  }
]

This is the server. When developing features, first inspect the schema.graphql file to view/create necessary types. Then run yarn run generate, and use those types throughout your implementation. Code in a functional style with immutability. When needing database interaction, use the ./knex/knex import which already has the db connection. 

# Attain Server - Codebase Overview

## Architecture
This is a Node.js/TypeScript backend server for an e-commerce/inventory management system with the following key components:

1. **GraphQL API**: Built with Apollo Server, providing the primary interface for client applications.
2. **RESTful API Endpoints**: Express.js routes for specific functionalities like order submission, invoice handling, and integration with external services.
3. **PostgreSQL Database**: Using Knex.js for query building and migrations.

## Core Components

### Server Setup
- `app.ts`: Entry point that starts the server with configured port
- `server.ts`: Main server configuration, initializing Apollo Server, Express, and API routes

### Data Layer
- **Database**: PostgreSQL with Knex.js for query building and migrations
- **GraphQL Schema**: Defined in `schema.graphql` with extensive types for the business domain

### Directory Structure
- `/src/apis`: REST API endpoint handlers for various functionalities
- `/src/graphql`: 
  - `/mutations`: GraphQL mutation resolvers
  - `/queries`: GraphQL query resolvers
- `/src/services`: Business logic organized by domain:
  - `orderService`: Order processing logic
  - `invoiceService`: Invoice generation and management
  - `userService`: User management
  - `itemService`: Product/inventory management
  - `cartService`: Shopping cart functionality
  - `supplierService`: Supplier management
  - `routeService`: Delivery route management
  - `notificationService`: Notifications system
  - `integrationService`: Third-party integrations
- `/src/config`: Environment and configuration settings
- `/src/util`: Utility functions
- `/migrations`: Database schema migrations

## Key Features
1. **Order Management**: Creating and processing orders
2. **Invoice Generation**: Creating and sending invoices
3. **Integration with QuickBooks**: OAuth authentication and data synchronization
4. **User Management**: Account creation and authentication
5. **Product Catalog**: Items, categories, and pricing
6. **Push Notifications**: Using Expo Server SDK
7. **Email Integration**: Through SendGrid
8. **File Storage**: AWS S3 integration

## External Integrations
- **QuickBooks**: Financial data integration
- **SendGrid**: Email services
- **Expo**: Push notifications
- **Firebase**: Authentication and possibly database

## Development Tools
- TypeScript for static typing
- Jest for testing
- ESLint and Prettier for code quality
- Database migrations with db-migrate
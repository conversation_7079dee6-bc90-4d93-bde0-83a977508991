Stack trace:
Frame         Function      Args
0007FFFFB750  00021005FEBA (000210285F48, 00021026AB6E, 000000000000, 0007FFFFA650) msys-2.0.dll+0x1FEBA
0007FFFFB750  0002100467F9 (000000000000, 000000000000, 000000000000, 0007FFFFBA28) msys-2.0.dll+0x67F9
0007FFFFB750  000210046832 (000210285FF9, 0007FFFFB608, 000000000000, 000000000000) msys-2.0.dll+0x6832
0007FFFFB750  000210068F86 (000000000000, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x28F86
0007FFFFB750  0002100690B4 (0007FFFFB760, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x290B4
0007FFFFBA30  00021006A49D (0007FFFFB760, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2A49D
End of stack trace
Loaded modules:
000100400000 bash.exe
7FFF2AB90000 ntdll.dll
7FFF295F0000 KERNEL32.DLL
7FFF28390000 KERNELBASE.dll
7FFF29BD0000 USER32.dll
7FFF28220000 win32u.dll
7FFF2A6A0000 GDI32.dll
7FFF27C80000 gdi32full.dll
7FFF27ED0000 msvcp_win.dll
000210040000 msys-2.0.dll
7FFF27DB0000 ucrtbase.dll
7FFF2A210000 advapi32.dll
7FFF29360000 msvcrt.dll
7FFF2A7D0000 sechost.dll
7FFF287F0000 bcrypt.dll
7FFF29210000 RPCRT4.dll
7FFF27420000 CRYPTBASE.DLL
7FFF28770000 bcryptPrimitives.dll
7FFF29410000 IMM32.DLL

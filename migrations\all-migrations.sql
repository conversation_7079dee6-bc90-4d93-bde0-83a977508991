-- This script was generated by the ERD tool in pgAdmin 4.
-- Please log an issue at https://github.com/pgadmin-org/pgadmin4/issues/new/choose if you find any bugs, including reproduction steps.
BEGIN;


CREATE TABLE IF NOT EXISTS public.account
(
    id serial NOT NULL,
    user_id integer,
    external_id character varying(255) COLLATE pg_catalog."default",
    is_default boolean,
    type text COLLATE pg_catalog."default",
    is_active boolean
);

CREATE TABLE IF NOT EXISTS public.attain_user
(
    id serial NOT NULL,
    name character varying(255) COLLATE pg_catalog."default",
    email character varying(255) COLLATE pg_catalog."default",
    store_id integer,
    buyer_id character varying(255) COLLATE pg_catalog."default",
    user_name character varying(255) COLLATE pg_catalog."default",
    address character varying(255) COLLATE pg_catalog."default",
    created_at timestamp without time zone DEFAULT CURRENT_TIMESTAMP,
    created_by text COLL<PERSON>E pg_catalog."default",
    password text COLLATE pg_catalog."default",
    timezone text COLLATE pg_catalog."default",
    supplier_beta boolean DEFAULT false,
    phone_number text COLLATE pg_catalog."default",
    approved boolean,
    ein text COLLATE pg_catalog."default",
    driver boolean,
    contact_email text COLLATE pg_catalog."default",
    service_type text COLLATE pg_catalog."default",
    store_group text COLLATE pg_catalog."default",
    route_id text COLLATE pg_catalog."default",
    qb_id text COLLATE pg_catalog."default",
    updated_at timestamp without time zone DEFAULT CURRENT_TIMESTAMP,
    config json,
    archived boolean,
    primary_route_id integer,
    CONSTRAINT attain_user_pkey PRIMARY KEY (id)
);

CREATE TABLE IF NOT EXISTS public.brand_spotlight
(
    id serial NOT NULL,
    supplier_id integer,
    user_id integer,
    active boolean,
    name text COLLATE pg_catalog."default",
    spotlight_image text COLLATE pg_catalog."default"
);

CREATE TABLE IF NOT EXISTS public.cart
(
    id serial NOT NULL,
    user_id integer,
    subtotal numeric,
    csv text COLLATE pg_catalog."default"
);

CREATE TABLE IF NOT EXISTS public.cart_csv
(
    id integer NOT NULL GENERATED ALWAYS AS IDENTITY ( INCREMENT 1 START 1 MINVALUE 1 MAXVALUE 2147483647 CACHE 1 ),
    cart_id integer,
    csv text COLLATE pg_catalog."default",
    supplier text COLLATE pg_catalog."default"
);

CREATE TABLE IF NOT EXISTS public.cart_item
(
    id serial NOT NULL,
    cart_id integer,
    item_id integer,
    quantity integer
);

CREATE TABLE IF NOT EXISTS public.category
(
    id serial NOT NULL,
    name character varying(255) COLLATE pg_catalog."default",
    supplier_id integer,
    image text COLLATE pg_catalog."default",
    ordering integer NOT NULL DEFAULT 0,
    CONSTRAINT category_pkey PRIMARY KEY (id)
);

CREATE TABLE IF NOT EXISTS public.category_item
(
    category_id integer,
    item_id integer,
    CONSTRAINT category_item_unique UNIQUE (category_id, item_id)
);

CREATE TABLE IF NOT EXISTS public.coremark_db
(
    id integer,
    name character varying(255) COLLATE pg_catalog."default",
    nacs_category character varying(255) COLLATE pg_catalog."default",
    nacs_subcategory character varying(255) COLLATE pg_catalog."default",
    unit_size character varying(255) COLLATE pg_catalog."default",
    upc1 character varying(255) COLLATE pg_catalog."default",
    upc2 character varying(255) COLLATE pg_catalog."default",
    price numeric,
    image text COLLATE pg_catalog."default",
    supplier_code character varying(255) COLLATE pg_catalog."default",
    correct_image boolean,
    supplier character varying(255) COLLATE pg_catalog."default",
    discounted_price numeric,
    oos boolean,
    upc3 character varying(255) COLLATE pg_catalog."default",
    upc4 character varying(255) COLLATE pg_catalog."default",
    size text COLLATE pg_catalog."default",
    outdated boolean,
    product_type text COLLATE pg_catalog."default",
    qoh integer,
    brand text COLLATE pg_catalog."default",
    sorting_category text COLLATE pg_catalog."default",
    sorting_order integer,
    parent_brand text COLLATE pg_catalog."default"
);

CREATE TABLE IF NOT EXISTS public.credit_request
(
    id integer NOT NULL GENERATED ALWAYS AS IDENTITY ( INCREMENT 1 START 1 MINVALUE 1 MAXVALUE 2147483647 CACHE 1 ),
    supplier_id integer,
    order_id integer,
    mispick boolean,
    damaged boolean,
    expired boolean,
    item_id integer,
    quantity integer,
    user_id integer,
    status text COLLATE pg_catalog."default",
    price_purchased_at double precision,
    CONSTRAINT credit_request_pkey PRIMARY KEY (id)
);

CREATE TABLE IF NOT EXISTS public.custom_item_price
(
    user_id integer,
    item_id integer,
    price double precision,
    CONSTRAINT price_user_item_unique UNIQUE (user_id, item_id)
);

CREATE TABLE IF NOT EXISTS public.deal_item
(
    id integer NOT NULL GENERATED ALWAYS AS IDENTITY ( INCREMENT 1 START 1 MINVALUE 1 MAXVALUE 2147483647 CACHE 1 ),
    deal_id integer,
    item_id integer,
    CONSTRAINT deal_item_pkey PRIMARY KEY (id)
);

CREATE TABLE IF NOT EXISTS public.deals
(
    id integer NOT NULL GENERATED ALWAYS AS IDENTITY ( INCREMENT 1 START 1 MINVALUE 1 MAXVALUE 2147483647 CACHE 1 ),
    type text COLLATE pg_catalog."default",
    quantity integer,
    discount numeric,
    CONSTRAINT deals_pkey PRIMARY KEY (id)
);

CREATE TABLE IF NOT EXISTS public.duffl
(
    product_name character varying(255) COLLATE pg_catalog."default",
    item_id integer,
    store_id text COLLATE pg_catalog."default",
    date date,
    quantity text COLLATE pg_catalog."default"
);

CREATE TABLE IF NOT EXISTS public.duffl_item
(
    id serial NOT NULL,
    name character varying(255) COLLATE pg_catalog."default",
    item_id integer,
    supplier character varying(255) COLLATE pg_catalog."default",
    local_item boolean,
    created_by integer,
    price text COLLATE pg_catalog."default",
    image text COLLATE pg_catalog."default",
    upc1 text COLLATE pg_catalog."default",
    upc2 text COLLATE pg_catalog."default",
    unit_size text COLLATE pg_catalog."default"
);

CREATE TABLE IF NOT EXISTS public.duffl_item_new
(
    id integer,
    name character varying(255) COLLATE pg_catalog."default",
    item_id character varying(255) COLLATE pg_catalog."default",
    supplier character varying(255) COLLATE pg_catalog."default",
    local_item boolean,
    created_by integer
);

CREATE TABLE IF NOT EXISTS public.duffl_order
(
    order_id integer,
    status text COLLATE pg_catalog."default",
    csv text COLLATE pg_catalog."default",
    user_id integer
);

CREATE TABLE IF NOT EXISTS public.employees
(
    id serial NOT NULL,
    supplier_id integer,
    name character varying(255) COLLATE pg_catalog."default",
    phone character varying(20) COLLATE pg_catalog."default",
    email character varying(255) COLLATE pg_catalog."default",
    app_access boolean DEFAULT false,
    dashboard_access boolean DEFAULT false,
    created_at timestamp without time zone NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at timestamp without time zone NOT NULL DEFAULT CURRENT_TIMESTAMP,
    last_login timestamp without time zone,
    archived boolean DEFAULT false,
    CONSTRAINT employees_pkey PRIMARY KEY (id)
);

CREATE TABLE IF NOT EXISTS public.invoice
(
    id serial NOT NULL,
    date_received timestamp without time zone,
    total numeric,
    order_id integer,
    invoice_id character varying(255) COLLATE pg_catalog."default",
    supplier_id integer,
    user_id integer,
    date_created timestamp without time zone DEFAULT CURRENT_TIMESTAMP,
    date_due date,
    subtotal numeric,
    notes text COLLATE pg_catalog."default",
    discount numeric,
    signature text COLLATE pg_catalog."default",
    signature_name text COLLATE pg_catalog."default",
    qb_id text COLLATE pg_catalog."default",
    updated_at timestamp without time zone DEFAULT CURRENT_TIMESTAMP,
    paid numeric DEFAULT 0,
    payment_method text COLLATE pg_catalog."default",
    credit numeric DEFAULT 0,
    archived boolean DEFAULT false,
    config json,
    route_id integer,
    CONSTRAINT invoice_pkey PRIMARY KEY (id),
    CONSTRAINT invoice_order_id_key UNIQUE (order_id)
);

CREATE TABLE IF NOT EXISTS public.invoice_item
(
    id serial NOT NULL,
    invoice_id integer,
    name character varying(255) COLLATE pg_catalog."default",
    quantity integer,
    unit_size character varying(255) COLLATE pg_catalog."default",
    size text COLLATE pg_catalog."default",
    upc1 character varying(255) COLLATE pg_catalog."default",
    upc2 character varying(255) COLLATE pg_catalog."default",
    price numeric,
    checked_in boolean,
    checked_in_quantity integer,
    is_mispick boolean,
    cog_price numeric,
    qb_id text COLLATE pg_catalog."default",
    item_id integer,
    CONSTRAINT invoice_item_pkey PRIMARY KEY (id)
);

CREATE TABLE IF NOT EXISTS public.item
(
    id serial NOT NULL,
    name character varying(255) COLLATE pg_catalog."default",
    nacs_category character varying(255) COLLATE pg_catalog."default",
    nacs_subcategory character varying(255) COLLATE pg_catalog."default",
    unit_size character varying(255) COLLATE pg_catalog."default",
    upc1 character varying(255) COLLATE pg_catalog."default",
    upc2 character varying(255) COLLATE pg_catalog."default",
    price numeric,
    image text COLLATE pg_catalog."default",
    supplier_code character varying(255) COLLATE pg_catalog."default",
    correct_image boolean,
    supplier character varying(255) COLLATE pg_catalog."default",
    discounted_price numeric,
    oos boolean,
    upc3 character varying(255) COLLATE pg_catalog."default",
    upc4 character varying(255) COLLATE pg_catalog."default",
    outdated boolean,
    size text COLLATE pg_catalog."default",
    local_item boolean,
    metadata text COLLATE pg_catalog."default",
    qoh integer,
    brand text COLLATE pg_catalog."default",
    created_at timestamp without time zone DEFAULT CURRENT_TIMESTAMP,
    sorting_category text COLLATE pg_catalog."default",
    "objectID" character varying(255) COLLATE pg_catalog."default",
    sorting_order integer,
    parent_brand text COLLATE pg_catalog."default",
    archived boolean NOT NULL,
    updated_at timestamp without time zone DEFAULT CURRENT_TIMESTAMP,
    qb_id text COLLATE pg_catalog."default",
    qb_sync_token text COLLATE pg_catalog."default",
    qty_on_hand integer DEFAULT 0,
    cog_price numeric,
    crv character varying(255) COLLATE pg_catalog."default",
    moq integer DEFAULT 0,
    CONSTRAINT item_pkey PRIMARY KEY (id)
);

CREATE TABLE IF NOT EXISTS public.migrations
(
    id serial NOT NULL,
    name character varying(255) COLLATE pg_catalog."default" NOT NULL,
    run_on timestamp without time zone NOT NULL,
    CONSTRAINT migrations_pkey PRIMARY KEY (id)
);

CREATE TABLE IF NOT EXISTS public.new_pitco
(
    id integer,
    name character varying(255) COLLATE pg_catalog."default",
    nacs_category character varying(255) COLLATE pg_catalog."default",
    nacs_subcategory character varying(255) COLLATE pg_catalog."default",
    unit_size character varying(255) COLLATE pg_catalog."default",
    upc1 character varying(255) COLLATE pg_catalog."default",
    upc2 character varying(255) COLLATE pg_catalog."default",
    price numeric,
    image text COLLATE pg_catalog."default",
    supplier_code character varying(255) COLLATE pg_catalog."default",
    correct_image boolean,
    supplier character varying(255) COLLATE pg_catalog."default",
    discounted_price numeric,
    oos boolean,
    upc3 character varying(255) COLLATE pg_catalog."default",
    upc4 character varying(255) COLLATE pg_catalog."default",
    outdated boolean,
    size text COLLATE pg_catalog."default",
    local_item boolean,
    metadata text COLLATE pg_catalog."default",
    sorting_category text COLLATE pg_catalog."default"
);

CREATE TABLE IF NOT EXISTS public.order_detail
(
    id serial NOT NULL,
    user_id integer,
    subtotal numeric,
    status character varying(255) COLLATE pg_catalog."default",
    date_submitted timestamp without time zone,
    single_supplier character varying(255) COLLATE pg_catalog."default",
    order_name character varying(255) COLLATE pg_catalog."default",
    discount double precision,
    notes text COLLATE pg_catalog."default",
    config json,
    CONSTRAINT order_detail_pkey PRIMARY KEY (id)
);

CREATE TABLE IF NOT EXISTS public.order_item
(
    id serial NOT NULL,
    order_id integer,
    item_id integer,
    quantity integer,
    new_supplier text COLLATE pg_catalog."default",
    oos boolean,
    price_purchased_at numeric
);

CREATE TABLE IF NOT EXISTS public.order_status
(
    id serial NOT NULL,
    order_id integer,
    supplier_id integer,
    delivery_date date,
    delivering_date date,
    submission_date date,
    date_delivery timestamp without time zone
);

CREATE TABLE IF NOT EXISTS public.push_notification_tokens
(
    user_id serial NOT NULL,
    token character varying(255) COLLATE pg_catalog."default" NOT NULL,
    CONSTRAINT push_notification_tokens_token_key UNIQUE (token)
);

CREATE TABLE IF NOT EXISTS public.recommendation
(
    id serial NOT NULL,
    item_id integer,
    num_store integer,
    quantity integer,
    is_trending boolean,
    user_id integer,
    CONSTRAINT recommendation_pkey PRIMARY KEY (id)
);

CREATE TABLE IF NOT EXISTS public.role_assignment
(
    employee_id integer,
    role_id integer,
    assigned_at timestamp without time zone NOT NULL DEFAULT CURRENT_TIMESTAMP
);

CREATE TABLE IF NOT EXISTS public.roles
(
    id serial NOT NULL,
    name character varying(50) COLLATE pg_catalog."default",
    description text COLLATE pg_catalog."default",
    created_at timestamp without time zone NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at timestamp without time zone NOT NULL DEFAULT CURRENT_TIMESTAMP,
    CONSTRAINT roles_pkey PRIMARY KEY (id)
);

CREATE TABLE IF NOT EXISTS public.route
(
    id serial NOT NULL,
    supplier_id integer,
    name text COLLATE pg_catalog."default",
    color text COLLATE pg_catalog."default",
    day_of_week text COLLATE pg_catalog."default",
    driver text COLLATE pg_catalog."default",
    config json,
    CONSTRAINT route_pkey PRIMARY KEY (id)
);

CREATE TABLE IF NOT EXISTS public.route_assignment
(
    id serial NOT NULL,
    route_id integer,
    employee_id integer,
    created_at timestamp without time zone NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at timestamp without time zone NOT NULL DEFAULT CURRENT_TIMESTAMP,
    CONSTRAINT route_assignment_pkey PRIMARY KEY (id)
);

CREATE TABLE IF NOT EXISTS public.scrapy_default_queue
(
    id serial NOT NULL,
    priority real,
    message text COLLATE pg_catalog."default",
    CONSTRAINT scrapy_default_queue_pkey PRIMARY KEY (id)
);

CREATE TABLE IF NOT EXISTS public.spotlight
(
    id serial NOT NULL,
    supplier_id integer,
    user_id integer,
    active boolean
);

CREATE TABLE IF NOT EXISTS public.supplier
(
    id integer NOT NULL,
    name character varying(255) COLLATE pg_catalog."default",
    logo text COLLATE pg_catalog."default",
    spotlight_image text COLLATE pg_catalog."default",
    need_signup boolean,
    minimum integer,
    address text COLLATE pg_catalog."default",
    phone_number text COLLATE pg_catalog."default",
    notification_number text COLLATE pg_catalog."default",
    notification_email text COLLATE pg_catalog."default",
    config json,
    qb_realm_id text COLLATE pg_catalog."default",
    qb_access_token text COLLATE pg_catalog."default",
    qb_refresh_token text COLLATE pg_catalog."default",
    email text COLLATE pg_catalog."default",
    CONSTRAINT supplier_pkey PRIMARY KEY (id)
);

CREATE TABLE IF NOT EXISTS public.supplier_config
(
    id serial NOT NULL,
    supplier_id integer,
    key character varying COLLATE pg_catalog."default" NOT NULL,
    value character varying COLLATE pg_catalog."default" NOT NULL,
    created_at timestamp without time zone NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at timestamp without time zone NOT NULL DEFAULT CURRENT_TIMESTAMP,
    CONSTRAINT supplier_config_pkey PRIMARY KEY (id)
);

CREATE TABLE IF NOT EXISTS public.supplier_times
(
    id serial NOT NULL,
    supplier_id integer,
    cutoff_time time without time zone,
    cutoff_day character varying(255) COLLATE pg_catalog."default",
    delivery_time time without time zone,
    delivery_day character varying(255) COLLATE pg_catalog."default",
    days_to_delivery integer,
    business_id integer
);

ALTER TABLE IF EXISTS public.attain_user
    ADD CONSTRAINT attain_user_primary_route_id_fkey FOREIGN KEY (primary_route_id)
    REFERENCES public.route (id) MATCH SIMPLE
    ON UPDATE NO ACTION
    ON DELETE NO ACTION;


ALTER TABLE IF EXISTS public.category
    ADD CONSTRAINT category_supplier_id_fkey FOREIGN KEY (supplier_id)
    REFERENCES public.supplier (id) MATCH SIMPLE
    ON UPDATE NO ACTION
    ON DELETE NO ACTION;


ALTER TABLE IF EXISTS public.category_item
    ADD CONSTRAINT category_item_category_id_fkey FOREIGN KEY (category_id)
    REFERENCES public.category (id) MATCH SIMPLE
    ON UPDATE NO ACTION
    ON DELETE NO ACTION;


ALTER TABLE IF EXISTS public.category_item
    ADD CONSTRAINT category_item_item_id_fkey FOREIGN KEY (item_id)
    REFERENCES public.item (id) MATCH SIMPLE
    ON UPDATE NO ACTION
    ON DELETE NO ACTION;


ALTER TABLE IF EXISTS public.credit_request
    ADD CONSTRAINT item_id_to_request FOREIGN KEY (item_id)
    REFERENCES public.item (id) MATCH SIMPLE
    ON UPDATE NO ACTION
    ON DELETE NO ACTION;


ALTER TABLE IF EXISTS public.credit_request
    ADD CONSTRAINT order_id_to_request FOREIGN KEY (order_id)
    REFERENCES public.order_detail (id) MATCH SIMPLE
    ON UPDATE NO ACTION
    ON DELETE NO ACTION;


ALTER TABLE IF EXISTS public.credit_request
    ADD CONSTRAINT supplier_id_to_request FOREIGN KEY (supplier_id)
    REFERENCES public.supplier (id) MATCH SIMPLE
    ON UPDATE NO ACTION
    ON DELETE NO ACTION;


ALTER TABLE IF EXISTS public.credit_request
    ADD CONSTRAINT user_id_to_request FOREIGN KEY (user_id)
    REFERENCES public.attain_user (id) MATCH SIMPLE
    ON UPDATE NO ACTION
    ON DELETE NO ACTION;


ALTER TABLE IF EXISTS public.custom_item_price
    ADD CONSTRAINT price_item_fk FOREIGN KEY (item_id)
    REFERENCES public.item (id) MATCH SIMPLE
    ON UPDATE NO ACTION
    ON DELETE NO ACTION;


ALTER TABLE IF EXISTS public.custom_item_price
    ADD CONSTRAINT price_user_fk FOREIGN KEY (user_id)
    REFERENCES public.attain_user (id) MATCH SIMPLE
    ON UPDATE NO ACTION
    ON DELETE NO ACTION;
CREATE INDEX IF NOT EXISTS price_user
    ON public.custom_item_price(user_id);


ALTER TABLE IF EXISTS public.employees
    ADD CONSTRAINT fk_employee_to_supplier FOREIGN KEY (supplier_id)
    REFERENCES public.supplier (id) MATCH SIMPLE
    ON UPDATE RESTRICT
    ON DELETE CASCADE;


ALTER TABLE IF EXISTS public.invoice
    ADD CONSTRAINT fk_invoice_to_order_detail FOREIGN KEY (order_id)
    REFERENCES public.order_detail (id) MATCH SIMPLE
    ON UPDATE NO ACTION
    ON DELETE NO ACTION;
CREATE INDEX IF NOT EXISTS invoice_order_id_key
    ON public.invoice(order_id);


ALTER TABLE IF EXISTS public.invoice
    ADD CONSTRAINT fk_invoice_to_supplier FOREIGN KEY (supplier_id)
    REFERENCES public.supplier (id) MATCH SIMPLE
    ON UPDATE NO ACTION
    ON DELETE NO ACTION;
CREATE INDEX IF NOT EXISTS idx_invoice_supplier
    ON public.invoice(supplier_id);


ALTER TABLE IF EXISTS public.invoice
    ADD CONSTRAINT invoice_route_id_fkey FOREIGN KEY (route_id)
    REFERENCES public.route (id) MATCH SIMPLE
    ON UPDATE NO ACTION
    ON DELETE NO ACTION;


ALTER TABLE IF EXISTS public.invoice_item
    ADD CONSTRAINT fk_invoice_item_to_invoice FOREIGN KEY (invoice_id)
    REFERENCES public.invoice (id) MATCH SIMPLE
    ON UPDATE NO ACTION
    ON DELETE NO ACTION;


ALTER TABLE IF EXISTS public.order_item
    ADD CONSTRAINT fk_item FOREIGN KEY (item_id)
    REFERENCES public.item (id) MATCH SIMPLE
    ON UPDATE NO ACTION
    ON DELETE NO ACTION;


ALTER TABLE IF EXISTS public.order_item
    ADD CONSTRAINT fk_order_item_to_order_detail FOREIGN KEY (order_id)
    REFERENCES public.order_detail (id) MATCH SIMPLE
    ON UPDATE NO ACTION
    ON DELETE NO ACTION;


ALTER TABLE IF EXISTS public.push_notification_tokens
    ADD CONSTRAINT fk_push_notification_tokens_to_attain_user FOREIGN KEY (user_id)
    REFERENCES public.attain_user (id) MATCH SIMPLE
    ON UPDATE NO ACTION
    ON DELETE CASCADE;


ALTER TABLE IF EXISTS public.role_assignment
    ADD CONSTRAINT fk_role_assignment_to_employee FOREIGN KEY (employee_id)
    REFERENCES public.employees (id) MATCH SIMPLE
    ON UPDATE RESTRICT
    ON DELETE CASCADE;


ALTER TABLE IF EXISTS public.role_assignment
    ADD CONSTRAINT fk_role_assignment_to_roles FOREIGN KEY (role_id)
    REFERENCES public.roles (id) MATCH SIMPLE
    ON UPDATE RESTRICT
    ON DELETE CASCADE;


ALTER TABLE IF EXISTS public.route
    ADD CONSTRAINT route_supplier_fk FOREIGN KEY (supplier_id)
    REFERENCES public.supplier (id) MATCH SIMPLE
    ON UPDATE NO ACTION
    ON DELETE NO ACTION;


ALTER TABLE IF EXISTS public.route_assignment
    ADD CONSTRAINT fk_route_assignment_to_employee FOREIGN KEY (employee_id)
    REFERENCES public.employees (id) MATCH SIMPLE
    ON UPDATE RESTRICT
    ON DELETE CASCADE;


ALTER TABLE IF EXISTS public.route_assignment
    ADD CONSTRAINT fk_route_assignment_to_route FOREIGN KEY (route_id)
    REFERENCES public.route (id) MATCH SIMPLE
    ON UPDATE RESTRICT
    ON DELETE CASCADE;


ALTER TABLE IF EXISTS public.supplier_config
    ADD CONSTRAINT supplier_config_supplier_id_fk FOREIGN KEY (supplier_id)
    REFERENCES public.supplier (id) MATCH SIMPLE
    ON UPDATE RESTRICT
    ON DELETE CASCADE;

-- Performance indexes for orderItemTotals query optimization
-- Composite index for invoice table - covers DSD supplier queries
CREATE INDEX IF NOT EXISTS idx_invoice_supplier_archived_date
    ON public.invoice(supplier_id, archived, date_created);

-- Composite index for order_detail table - covers non-DSD supplier queries and presale queries
CREATE INDEX IF NOT EXISTS idx_order_detail_supplier_date_status
    ON public.order_detail(single_supplier, date_submitted, status);

-- Composite index for item table - covers item lookups by supplier
CREATE INDEX IF NOT EXISTS idx_item_supplier_archived
    ON public.item(supplier, archived);

-- Composite index for invoice_item table - optimizes joins
CREATE INDEX IF NOT EXISTS idx_invoice_item_invoice_item
    ON public.invoice_item(invoice_id, item_id);

-- Composite index for order_item table - optimizes joins
CREATE INDEX IF NOT EXISTS idx_order_item_order_item
    ON public.order_item(order_id, item_id);

CREATE TABLE IF NOT EXISTS public.sync_status
(
    id serial NOT NULL,
    supplier_id integer NOT NULL,
    type character varying(50) NOT NULL,
    status character varying(20) NOT NULL,
    error_message text,
    start_time timestamp without time zone,
    end_time timestamp without time zone,
    duration_ms integer,
    last_attempt_at timestamp without time zone NOT NULL DEFAULT CURRENT_TIMESTAMP,
    created_at timestamp without time zone NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at timestamp without time zone NOT NULL DEFAULT CURRENT_TIMESTAMP,
    archived_at timestamp without time zone,
    CONSTRAINT sync_status_pkey PRIMARY KEY (id)
);

ALTER TABLE IF EXISTS public.sync_status
    ADD CONSTRAINT fk_sync_status_supplier FOREIGN KEY (supplier_id)
    REFERENCES public.supplier (id) MATCH SIMPLE
    ON UPDATE CASCADE
    ON DELETE NO ACTION;

CREATE INDEX IF NOT EXISTS idx_sync_status_supplier_type
    ON public.sync_status(supplier_id, type);
    
END;

scalar JSON
scalar JSONObject
scalar Time
scalar Date
scalar DateTime

enum Ordering {
  ASC
  DESC
}

input SortBy {
  field: String!
  ordering: Ordering!
}

input CreateCartInput {
  userId: ID!
}

input AddItemToCartInput {
  cartId: ID!
  itemId: ID!
  quantity: Int!
}
input UpdateItemInCartInput {
  cartId: ID!
  itemId: ID!
  quantity: Int!
}

input GetCartsInput {
  ids: [ID]
  userId: ID
  userIds: [ID]
}

input UpdateDefaultInAccountInput {
  isDefault: Boolean
  accountId: ID!
  userId: ID!
}

input PaginationInput {
  offset: Int
  limit: Int
}

input GetItemsInput {
  userId: ID
  ids: [ID]
  upcs: [ID]
  pagination: PaginationInput
}

input GetItemsBySupplierInput {
  userId: ID
  supplierId: ID
  upc: ID
  pagination: PaginationInput
  query: String
  brand: String
  category: String
  section: String
  ids: [ID]
}

input GetItemsByFilterInput {
  userId: ID
  category: String
  tag: String
  pagination: PaginationInput
}

input GetCategoriesInput {
  userId: ID
  supplierId: ID
  pagination: PaginationInput
}

input GetSectionsInput {
  userId: ID
  sections: [String]
  pagination: PaginationInput
}

input GetCutoffTimesInput {
  businessId: ID!
}

input SubmitOrderInput {
  cartId: ID!
  userId: ID!
  supplier: String
  notes: String
  discount: Float
  isCredit: Boolean
  deliveryDate: Date
  config: JSONObject
}

input GetOrdersInput {
  ids: [ID]
  userId: ID
  pagination: PaginationInput
  query: String
  status: String
  supplierId: ID
}

input GetUsersInput {
  ids: [ID]
  supplierId: ID
  pagination: PaginationInput
  includeCustomPrices: Boolean
  search: String
}

input SubmitFeedbackInput {
  userId: ID!
  issues: String
  thumbsUp: Boolean!
}

input CreateUserInput {
  name: String
  password: String
  user_name: String
  suppliers: [ID!]!
  phone_number: String
  address: String
  created_by: String
  delivery_window: DeliveryWindowInput
  ein: String
  contact_email: String
  route_id: ID
  store_group: String
  custom_prices: [CustomPriceWithoutUserIdInput]
  hidden_products: [ID!]
  config: JSONObject
  qb_id: String
  created_at: DateTime
  updated_at: DateTime
  net_terms_days: Int
}

input DeliveryWindowInput {
  days_of_week: [String]
  start_time: String
  end_time: String
}

type Tag {
  name: String
  value: String
}

input GetTagsInput {
  category: String
  pagination: PaginationInput
}

input GetInvoicesInput {
  ids: [ID]
  supplierId: ID
  orderIds: [ID]
  query: String
  pagination: PaginationInput
  onlyWithCredits: Boolean
}

input GetSuppliersInput {
  ids: [ID]
  pagination: PaginationInput
}

input GetCategoriesBySupplierInput {
  supplierId: ID
  fillItemsData: Boolean
}

input GetRoutesBySupplierInput {
  supplierId: ID!
}

input GetRoutesInput {
  ids: [ID!]!
}

type Query {
  orders(getOrdersInput: GetOrdersInput): [Order]!
  carts(getCartsInput: GetCartsInput!): [Cart]!
  items(getItemsInput: GetItemsInput): [Item]!
  users(getUsersInput: GetUsersInput): [User]!
  usersByFilter(getUsersByFilterInput: GetUsersByFilterInput): UsersByFilter
  tags(getTagsInput: GetTagsInput): [Tag]!
  itemsByFilter(getItemsByFilterInput: GetItemsByFilterInput): [Item]!
  categories(getCategoriesInput: GetCategoriesInput): [BasicCategory]!
  cutoffTimes(getCutoffTimesInput: GetCutoffTimesInput): [CutoffTime]!
  itemsBySupplier(getItemsBySupplierInput: GetItemsBySupplierInput): [Item]!
  itemsAvailableDuffl(
    businessId: ID
    supplier: String
    items: [ItemAvailableInput]
    message: String
  ): [ItemAvailable]
  sections(getSectionsInput: GetSectionsInput): [Section]!
  balanceLink(businessId: ID): BalanceLink
  accounts(businessId: ID): [Account]
  orderStatuses(orderId: ID): [OrderStatus]
  orderBySupplier(orderId: ID, supplierId: ID): OrderBySupplier
  uploadedOrderCsv(embedId: ID, sheetId: ID): UploadedOrderCsv
  spotlights(userId: ID): [Supplier]
  brandSpotlights(userId: ID): [BrandSpotlights]
  brandSections(getSectionsInput: GetSectionsInput): [Supplier]!
  suppliers(getSuppliersInput: GetSuppliersInput): [Supplier]!
  invoice(orderId: ID!): InvoiceWithStatus
  invoiceItems(orderId: ID, invoiceId: ID): [InvoiceItem]!
  invoiceItemMatch(
    getInvoiceItemMatchInput: GetInvoiceItemMatchInput
  ): InvoiceItemMatch
  actionItems(getActionItemsInput: GetActionItemsInput): [ActionItem]
  pushNotificationTokens(
    getPushNotificationTokensInput: GetPushNotificationTokensInput
  ): [PushNotificationToken]
  recommendations(
    getRecommendationsInput: GetRecommendationsInput
  ): [Recommendation]!
  invoices(getInvoicesInput: GetInvoicesInput): [Invoice]
  dashboardMetrics(
    supplier_id: ID!
    duration: Int
    dateRange: [DateTime]
    routeIds: [ID!]
    driver: String
    serviceType: String
  ): DashboardMetrics
  customerGroups(supplierId: ID): [CustomerGroup]
  creditRequests(userId: ID, supplierId: ID, orderId: ID): [CreditRequest]
  categoriesBySupplier(
    getCategoriesBySupplierInput: GetCategoriesBySupplierInput
  ): [Category]
  routesBySupplier(getRoutesBySupplierInput: GetRoutesBySupplierInput): [Route]
  routes(getRoutesInput: GetRoutesInput): [Route]
  orderItemTotals(
    getOrderItemTotalsInput: GetOrderItemTotalsInput
  ): [OrderItemTotal!]!
  routeTotals(getRouteTotalsInput: GetRouteTotalsInput): [RouteTotal!]!
  ordersBySupplier(
    getOrdersBySupplierInput: GetOrdersBySupplierInput
  ): OrdersBySupplier!
  invoicesBySupplier(
    getInvoicesBySupplierInput: GetInvoicesBySupplierInput
  ): InvoicesBySupplier!
  supplierConfig(supplierId: ID!): SupplierConfig!
  usersOnTodaysRoutes(supplierId: ID!): [User]!
  expectedOrders(supplierId: ID!, date: Date): [Order]!

  # v2 queries
  usersV2(usersInput: UsersInputV2!): UsersOutputV2!
  itemsV2(itemsInput: ItemsInputV2!): ItemsOutputV2!
  ordersV2(ordersInput: OrdersInputV2!): OrdersOutputV2!
  routesV2(routesInput: RoutesInputV2!): [Route]!
  groupPricesV2(groupPricesInput: GroupPricesInputV2!): GroupPricesOutputV2!
  allCustomPricesV2(
    allCustomPricesInput: AllCustomPricesInputV2!
  ): AllCustomPricesOutputV2!
  employeesV2(employeesInput: EmployeesInputV2!): EmployeesOutputV2!
  customerHiddenProducts(
    customerHiddenProductsInput: CustomerHiddenProductsInput!
  ): CustomerHiddenProductsOutput!
  getSyncingStatus(supplierId: ID!): SyncStatusResponse!
}

interface BaseOutput {
  totalCount: Int!
}

input UsersInputV2 {
  supplierId: ID
  filters: UsersFiltersV2
  pagination: PaginationInput
  sortBy: SortBy
  # I'd like to remove this, but the app needs it for efficient caching
  includeCustomPrices: Boolean
}

input UsersFiltersV2 {
  ids: [ID!]
  active: Boolean
  routeIds: [ID!]
  includeUnassigned: Boolean
  drivers: [String!]
  searchTerm: String
}

type UsersOutputV2 implements BaseOutput {
  users: [User]!
  totalCount: Int!
}

input ItemsInputV2 {
  supplierId: ID!
  userId: ID
  filters: ItemsFiltersV2
  pagination: PaginationInput
  sortBy: SortBy
}

input ItemsFiltersV2 {
  ids: [ID!]
  upc: String
  query: String
  brand: String
  category: String
  section: String
  archived: Boolean
  name: String
}

type ItemsOutputV2 implements BaseOutput {
  items: [Item]!
  totalCount: Int!
}

input OrdersInputV2 {
  supplierId: ID!
  filters: OrdersFiltersV2
  pagination: PaginationInput
  sortBy: SortBy
}

input RoutesInputV2 {
  supplierId: ID!
  filters: RoutesFiltersV2
}

input RoutesFiltersV2 {
  routeId: ID
}

input OrdersFiltersV2 {
  ids: [ID!]
  status: String
  deliveryDate: Date
  deliveryDateRange: [Date!]
  routeIds: [String!]
  driver: String
  userIds: [String!]
  userId: String
  signed: Boolean
  lastPaidDate: Date
  query: String
  perUserLimit: Int
  paidStatus: String
}

type OrdersOutputV2 implements BaseOutput {
  orders: [Order]!
  totalCount: Int!
}

input GroupPricesInputV2 {
  supplierId: ID!
  groupName: String
}

type GroupPrices {
  group: String!
  prices: [CustomPriceWithoutUserId]
}

type GroupPricesOutputV2 implements BaseOutput {
  groupPrices: [GroupPrices]
  totalCount: Int!
}

input AllCustomPricesInputV2 {
  supplierId: ID!
}

type AllCustomPricesOutputV2 implements BaseOutput {
  userCustomPrices: [UserCustomPrices]
  totalCount: Int!
}

type UserCustomPrices {
  userId: ID!
  prices: [CustomPriceWithoutUserId]
}

type CustomerHiddenProductsOutput {
  items: [CustomerHiddenProduct]!
}

type CustomerHiddenProduct {
  supplierId: ID
  userId: ID
  itemId: ID!
}

type CreditRequest {
  id: ID!
  user_id: ID!
  supplier_id: ID!
  order_id: ID!
  item_id: ID!
  quantity: Int!
  mispick: Boolean
  expired: Boolean
  damaged: Boolean
  customerDetails: User
  itemDetails: Item
  price_purchased_at: Float
  status: String
  image: String
}

type Deal {
  deal_id: ID
  type: String
  discount: Float
  quantity: Int
  item_id: ID
}

type UploadedOrderCsv {
  download_url: String
}

type OrderBySupplier {
  orderItems: [CartItem]
}

type OrderStatus {
  id: ID
  order_id: ID
  supplier_id: ID
  delivery_date: Float
  delivering_date: Float
  submission_date: Float
  name: String
}
type Account {
  id: String
  balanceBankAccount: BalanceBankAccount
  balanceCreditCard: BalanceCreditCard
  is_default: Boolean
  type: String
}

type BalanceBankAccount {
  institutionName: String
  accountName: String
  accountNumberMask: String
}

type BalanceCreditCard {
  brand: String
  expiredMonth: String
  expiredYear: String
  last4: String
}

type BalanceLink {
  link: String
}
type ItemAvailable {
  item_id: ID
  name: String
  mapped: Boolean
  supplier: String
  quantity: Int
}
type BasicCategory {
  name: String
  value: String
  image: String
}

type CutoffTime {
  id: String
  supplier: String!
  supplierInfo: Supplier
  cutoffDay: String
  cutoffTime: String
  deliveryDay: String
  deliveryTime: String
  daysToDelivery: Int
}

type Section {
  name: String
  image: String
  value: String
  items: [Item]
}

type OrdersBySupplier {
  orders: [Order!]!
  totalCount: Int!
}

input GetOrdersBySupplierFilters {
  ids: [ID!]
  status: String
  deliveryDate: Date
  deliveryDateRange: [Date!]
  routeIds: [String!]
  driver: String
  userId: String
  userIds: [String!]
  signed: Boolean
  query: String
}

input GetOrdersBySupplierInput {
  supplierId: ID!
  filters: GetOrdersBySupplierFilters
  pagination: PaginationInput
  sortBy: SortBy
}

input GetUsersFilters {
  active: String
  route: String
  driver: String
  searchTerm: String
  hasUnpaidBalance: Boolean
}

input GetUsersByFilterInput {
  supplierId: ID
  filters: GetUsersFilters
  pagination: PaginationInput
  includeCustomPrices: Boolean
  sortBy: SortBy
}

type UsersByFilter {
  users: [User]
  totalCount: Float
}

type CustomerGroup {
  group: String
  active: Int
  count: Int
}

type InvoicesBySupplier {
  invoices: [Invoice!]!
  totalCount: Int!
  totalProfit: Float!
  totalCredit: Float!
  paymentTotals: JSONObject!
  totalCases: Int!
}

input GetInvoicesBySupplierFilters {
  status: String
  query: String
  dateRange: [DateTime!]
  routeIds: [String!]
  driver: String
  userId: String
  lastPaidDate: Date
  signed: Boolean
  paidStatus: String
  deliveryDate: DateTime
}

input GetInvoicesBySupplierInput {
  supplierId: ID!
  filters: GetInvoicesBySupplierFilters
  pagination: PaginationInput
  sortBy: SortBy
}

type Mutation {
  createCart(cart: CreateCartInput!): Cart
  updateItemInCart(updateItemInCartInput: UpdateItemInCartInput!): Cart
  submitOrder(submitOrderInput: SubmitOrderInput!): Order
  submitFeedback(submitFeedbackInput: SubmitFeedbackInput!): String
  createAccountFromBalance(payload: String!): String
  updateDefaultInAccount(
    updateDefaultInAccountInput: UpdateDefaultInAccountInput
  ): [Account]
  captureTransaction(captureTransactionInput: CaptureTransactionInput): String
  updateOrder(updateOrderInput: UpdateOrderInput): String
  createOrder(createOrderInput: CreateOrderInput): Order
  createUser(createUserInput: CreateUserInput): [String]
  addInvoice(addInvoiceInput: AddInvoiceInput!): Invoice
  addInvoiceItems(addInvoiceItemsInput: AddInvoiceItemsInput!): [InvoiceItem]
  reconcileInvoiceWithItem(
    reconcileInvoiceWithItemInput: ReconcileInvoiceWithItemInput
  ): InvoiceItem
  sendReceivingReport(userId: ID, invoiceId: ID): String
  updateUserSuppliers(
    updateUserSuppliersInput: UpdateUserSuppliersInput
  ): [CutoffTime]
  addPushNotificationToken(
    addPushNotificationTokenInput: AddPushNotificationTokenInput
  ): PushNotificationToken
  submitCreditRequests(
    submitCreditRequestsInput: SubmitCreditRequestsInput
  ): String
  upsertItems(items: [ItemInput!]!, updateCategoryCustomPrices: Boolean): [Item]
  deleteItems(itemIds: [ID!]!): [ID]
  updateUser(user: UserInput!): User
  deleteUsers(userIds: [ID!]!): [ID]
  updateInvoice(updateInvoiceInput: UpdateInvoiceInput!): Invoice
  createBalanceTransaction(
    createBalanceTransactionInput: CreateBalanceTransactionInput!
  ): String
  updateCreditRequestStatus(id: ID!, status: String!): CreditRequest
  approveUser(id: ID!): User
  upsertCategory(categoryInput: CategoryInput!, skipItems: Boolean): Category
  deleteCategory(id: ID!): ID
  upsertRoute(routeInput: RouteInput!): Route
  deleteRoute(id: ID!): ID
  upsertCustomPrices(customPrices: [CustomPriceInput!]!): [CustomPrice]
  upsertAllCustomPrices(
    supplierId: ID!
    userId: ID!
    itemId: ID!
    price: Float!
    overrideExistingPrices: Boolean
  ): Boolean!
  createSupplier(input: CreateSupplierInput!): Supplier
  createSupplierConfig(input: CreateSupplierConfigInput!): SupplierConfig!
  updateSupplierConfig(input: UpdateSupplierConfigInput!): SupplierConfig!
  createInvoice(supplierId: ID!, orderId: ID!): Invoice
  createEmployee(input: CreateEmployeeInput!): Employee
  updateEmployee(input: UpdateEmployeeInput!): Employee
  startSync(input: StartSyncInput!): Boolean!
  reorderCategories(input: ReorderCategoriesInput!): Boolean!
  updateCategoryOrder(
    supplierId: ID!
    categoryId: ID!
    newOrder: Int!
  ): Category
}

input CreateSupplierInput {
  name: String!
  email: String!
  password: String!
  logo: String
  spotlight_image: String
  need_signup: Boolean
  minimum: Int
  phone_number: String
  address: String
  config: JSONObject
}

type SupplierConfig {
  show_order_tabs: Boolean
  send_back_in_stock_notification: Boolean
  send_daily_back_in_stock_notification: Boolean
}

input CreateSupplierConfigInput {
  supplierId: ID!
  show_order_tabs: Boolean
}

input UpdateSupplierConfigInput {
  supplierId: ID!
  show_order_tabs: Boolean
}

input CreateBalanceTransactionInput {
  amount: Float!
  userId: ID!
  title: String!
}
input SubmitCreditRequestsInput {
  userId: ID!
  supplier: String!
  orderId: ID
  creditRequests: [CreditRequestInput]
}

input CreditRequestInput {
  itemId: ID!
  quantity: Int!
  isMispick: Boolean
  isExpired: Boolean
  isDamaged: Boolean
  pricePurchasedAt: Float
}

input ItemAvailableInput {
  name: String
  unit_size: Int
  price: Float
  upc: String
  supplier: String
  quantity: Int
}

input UpdateOrderInput {
  supplierId: ID
  orderId: ID!
  orderName: String
  notes: String
  userId: ID!
  status: String
  deliveryDate: Date
  orderItems: [UpdateOrderItemInput]
  config: JSONObject
  subtotal: Float
  invoice: UpdateInvoiceInput
  netTermsDays: Int
}

input UpdateOrderItemInput {
  id: ID!
  price_purchased_at: Float!
  quantity: Int!
}

input CreateOrderInput {
  supplierId: ID!
  userId: ID!
  deliveryDate: Date
  orderItems: [UpdateOrderItemInput]
  orderName: String
  notes: String
  config: JSONObject
}

input CaptureTransactionInput {
  transactionId: String
  amount: Float
}

type Cart {
  id: ID!
  userId: ID!
  cartItems: [CartItem]
  subCarts: [SubCart]
  subtotal: Float
}

type SubCart {
  supplier: String
  cartItems: [CartItem]
  discount: Float
  minimum: Int
}

type CustomPrice {
  item_id: ID!
  user_id: ID!
  price: Float!
}

type CustomPriceWithoutUserId {
  item_id: ID!
  price: Float!
}

type User {
  id: ID!
  name: String
  email: String
  user_name: String
  suppliers: [Supplier]
  address: String
  supplier_beta: Boolean
  phone_number: String
  approved: Boolean
  ein: String
  driver: Boolean
  contact_email: String
  store_group: String
  route_id: ID
  custom_prices: [CustomPriceWithoutUserId]
  config: JSONObject
  qb_id: String
  unpaid_balance: Float
  delivery_window: DeliveryWindow
  archived: Boolean
  created_by: String
  created_at: DateTime
  updated_at: DateTime
  net_terms_days: Int
}

type DeliveryWindow {
  days_of_week: [String]
  start_time: String
  end_time: String
}

type Order {
  id: ID!
  order_number: Int
  status: String
  subtotal: Float!
  discount: Float
  date_submitted: Float
  orderItems: [CartItem]
  orderName: String
  delivery_date: Date
  supplier: String
  supplier_logo: String
  customerDetails: User
  invoice: Invoice
  totalQuantity: Int
  notes: String
  config: JSONObject
}

type CartItem {
  id: ID!
  name: String!
  unit_size: String
  size: String
  qoh: Int
  price: Float
  discounted_price: Float
  price_purchased_at: Float
  upc1: ID
  upc2: ID
  nacs_category: String
  nacs_subcategory: String
  quantity: Int
  item_id: ID!
  image: String
  supplier_code: String
  supplier: String
  oos: Boolean
  metadata: String
  crv: String
  cog_price: Float
  qb_id: String
  archived: Boolean
}

input CreateTable {
  cartID: ID!
  itemID: ID!
  quantity: Int!
}

type Item {
  id: ID!
  name: String!
  unit_size: String
  qoh: Int
  price: Float
  discounted_price: Float
  upc1: ID
  upc2: ID
  nacs_category: String
  nacs_subcategory: String
  image: String
  supplier_code: String
  related_items: [Item]
  supplier: String
  supplier_info: Supplier
  last_ordered_date: Float
  avg_cases_per_week: Float
  size: String
  tags: [ItemTag]
  oos: Boolean
  back_in_stock_date: DateTime
  local_item: Boolean
  outdated: Boolean
  created_at: DateTime
  archived: Boolean
  qb_id: String
  qb_sync_token: String
  updated_at: DateTime
  metadata: String
  crv: String
  qty_on_hand: Int
  cog_price: Float
  moq: Int
}

enum ItemTag {
  NEW
  OOS
  SALE
  ARCHIVED
}

type Supplier {
  id: ID!
  name: String!
  logo: String
  spotlight_image: String
  itemsPreview: [Item]
  need_signup: Boolean
  minimum: Int
  phone_number: String
  address: String
  config: JSONObject
  orderCount: Int
  qb_realm_id: String
  email: String
}

type BrandSpotlights {
  id: ID!
  name: String!
  spotlight_image: String
}

type Invoice {
  id: ID
  order_id: ID
  order_number: Int
  total: Float
  date_created: DateTime
  date_received: Float
  supplier_id: ID!
  invoice_id: String
  customerDetails: User
  invoiceItems: [InvoiceItem]
  subtotal: Float
  orderDetails: Order
  notes: String
  discount: Float
  signature: String
  signature_name: String
  paid: Float
  payment_method: String
  credit: Float
  qb_id: String
  updated_at: DateTime
  archived: Boolean
  config: JSONObject
}

type InvoiceItem {
  id: ID!
  invoice_id: ID!
  name: String!
  quantity: Int
  upc1: ID
  upc2: ID
  upc3: ID
  upc4: ID
  price: Float
  checked_in: Boolean
  checked_in_quantity: Int
  is_mispick: Boolean
  size: String
  unit_size: String
  cog_price: Float
  qb_id: String
  item_id: ID
  metadata: String
}

type InvoiceWithStatus {
  invoice: Invoice
  processing: Boolean
}

input InvoiceItemInput {
  id: ID
  invoice_id: ID
  name: String!
  quantity: Int
  upc1: ID
  upc2: ID
  upc3: ID
  upc4: ID
  price: Float
  size: String
  unit_size: String
  cog_price: Float
  qb_id: String
  item_id: ID
  is_mispick: Boolean
  checked_in: Boolean
  checked_in_quantity: Int
}

input AddInvoiceInput {
  date_created: DateTime
  date_received: DateTime
  order_id: ID
  order_number: Int
  total: Float
  supplier_id: ID!
  invoice_id: String
  invoice_items: [InvoiceItemInput!]
  user_id: ID
  subtotal: Float!
  return_items: Boolean
  notes: String
  paid: Float
  payment_method: String
  credit: Float
  discount: Float
  updated_at: DateTime
  config: JSONObject
}

input ReconcileInvoiceWithItemInput {
  invoice_id: ID
  invoice_item_id: ID
  quantity: Int
  is_mispick: Boolean
  checked_in: Boolean
}

input AddInvoiceItemsInput {
  invoice_id: ID
  invoice_items: [InvoiceItemInput!]
}

input GetInvoiceItemMatchInput {
  upc: String!
  invoiceId: ID!
}

input UpdateUserSuppliersInput {
  suppliers: [UserSupplierInput]
}

input UserSupplierInput {
  id: String
  cutoffDay: String
  cutoffTime: String
  deliveryDay: String
  deliveryTime: String
  daysToDelivery: Int
}
type InvoiceItemMatch {
  itemMatches: [InvoiceItem]
  matchStatus: MatchStatus
}

enum MatchStatus {
  single
  multiple
  suggested
  none
}

type ActionItem {
  description: String
  invoiceItem: InvoiceItem
}

input GetActionItemsInput {
  invoice_id: ID
  invoice_item_id: ID
}

type PushNotificationToken {
  user_id: ID!
  token: String!
}

input GetPushNotificationTokensInput {
  user_ids: [ID!]
}

input AddPushNotificationTokenInput {
  user_id: ID!
  token: String!
}

input GetRecommendationsInput {
  userId: ID!
  limit: Int
}
type Recommendation {
  id: ID!
  item_id: ID!
  num_store: Int
  quantity: Int
  is_trending: Boolean
  user_id: ID!
  item: Item
}

type DashboardMetricBrandBreakdown {
  brand: String
  quantity: Int
  breakdown: Float
}
type DashboardMetricBestSeller {
  name: String
  quantity: Int
  image: String
}
enum DashboardMetricType {
  ACTIVE_BUYERS
  FIRST_ORDERS
  AVG_ORDER_VALUE
  NUM_ORDERS
  GMV_TOTAL
  CASES_ORDERED
  NUM_INVOICES
  NUM_UNCONFIRMED_ORDERS
  NUM_USERS
  NUM_CANCELED_ORDERS
  NUM_CONFIRMED_ORDERS
  BRAND_BREAKDOWN
  BEST_SELLERS
  NUM_UNAPPROVED_CUSTOMERS
  PROFIT_TOTAL
  CREDIT_TOTAL
  NET_REVENUE
}
type DashboardMetrics {
  ACTIVE_BUYERS: Int
  FIRST_ORDERS: Int
  AVG_ORDER_VALUE: Float
  NUM_ORDERS: Int
  GMV_TOTAL: Float
  CASES_ORDERED: Int
  NUM_INVOICES: Int
  NUM_UNCONFIRMED_ORDERS: Int
  NUM_USERS: Int
  NUM_CANCELED_ORDERS: Int
  NUM_CONFIRMED_ORDERS: Int
  BRAND_BREAKDOWN: [DashboardMetricBrandBreakdown]
  BEST_SELLERS: [DashboardMetricBestSeller]
  NUM_UNAPPROVED_CUSTOMERS: Int
  PROFIT_TOTAL: Float
  CREDIT_TOTAL: Float
  NET_REVENUE: Float
}

input ItemInput {
  id: ID
  name: String
  unit_size: String
  qoh: Int
  price: Float
  discounted_price: Float
  upc1: ID
  upc2: ID
  nacs_category: String
  nacs_subcategory: String
  image: String
  supplier_code: String
  supplier: String
  size: String
  oos: Boolean
  back_in_stock_date: DateTime
  categoryIds: [ID]
  qb_id: String
  qb_sync_token: String
  updated_at: DateTime
  metadata: String
  crv: String
  qty_on_hand: Int
  cog_price: Float
  moq: Int
}

input CustomPriceInput {
  item_id: ID!
  user_id: ID!
  price: Float!
}

input CustomPriceWithoutUserIdInput {
  item_id: ID!
  price: Float!
}

input UserInput {
  id: ID!
  name: String
  email: String
  user_name: String
  address: String
  supplier_beta: Boolean
  phone_number: String
  approved: Boolean
  contact_email: String
  delivery_window: DeliveryWindowInput
  route_id: ID
  store_group: String
  custom_prices: [CustomPriceWithoutUserIdInput]
  hidden_products: [ID!]
  config: JSONObject
  qb_id: String
  updated_at: DateTime
  net_terms_days: Int
}

input UpdateInvoiceInput {
  id: ID!
  order_id: ID
  order_number: Int
  date_created: DateTime
  total: Float
  supplier_id: ID
  invoice_id: String
  invoice_items: [InvoiceItemInput!]
  user_id: ID
  subtotal: Float
  return_items: Boolean
  signature: String
  signature_name: String
  paid: Float
  payment_method: String
  credit: Float
  discount: Float
  notes: String
  qb_id: String
  updated_at: DateTime
  archived: Boolean
  config: JSONObject
}

type Category {
  id: ID!
  name: String!
  supplier_id: ID!
  image: String
  items: [Item]
  ordering: Int
}

input CategoryInput {
  id: ID
  supplier_id: ID!
  name: String!
  image: String
  items: [ItemInput]
  ordering: Int
}

type Route {
  id: ID!
  supplier_id: ID!
  name: String!
  color: String!
  day_of_week: String!
  driver: String
  config: JSONObject
}

input RouteInput {
  id: ID
  supplier_id: ID
  name: String
  color: String
  day_of_week: String
  driver: String
  config: JSONObject
}

type OrderItemTotal {
  id: ID!
  name: String!
  nacs_category: String
  weekly_totals: [Int!]!
  next_day_presale_totals: Int!
}

input GetOrderItemTotalsInput {
  supplierId: ID!
  numWeeks: Int!
  routeId: ID
  dayOffset: Int
  currentTz: String
}

type RouteTotal {
  id: ID!
  name: String!
  weekly_totals: [Int!]!
}

input GetRouteTotalsInput {
  supplierId: ID!
  numWeeks: Int!
  dayOffset: Int
  currentTz: String
}

input EmployeesInputV2 {
  supplierId: ID!
  filters: EmployeesFiltersV2
  pagination: PaginationInput
  sortBy: SortBy
}

input EmployeesFiltersV2 {
  ids: [ID]
  name: String
  email: String
  phone: String
  appAccess: Boolean
  dashboardAccess: Boolean
  roleIds: [ID]
  includeArchived: Boolean
  query: String
  createdAfter: DateTime
  createdBefore: DateTime
  lastLoginAfter: DateTime
  lastLoginBefore: DateTime
}

type Employee {
  id: ID!
  name: String!
  phone: String
  email: String!
  app_access: Boolean!
  dashboard_access: Boolean!
  created_at: DateTime!
  updated_at: DateTime!
  last_login: DateTime
  archived: Boolean!
  roles: [Role!]
  routes: [Route!]
}

type Role {
  id: ID!
  name: String!
  description: String
}

type EmployeesOutputV2 implements BaseOutput {
  employees: [Employee!]!
  totalCount: Int!
}

input CreateEmployeeInput {
  supplierId: ID!
  name: String!
  phone: String!
  email: String!
  password: String!
  appAccess: Boolean
  dashboardAccess: Boolean
  roleIds: [ID!]
  routeIds: [ID!]
}

input UpdateEmployeeInput {
  id: ID!
  name: String
  phone: String
  email: String
  password: String
  appAccess: Boolean
  dashboardAccess: Boolean
  lastLogin: DateTime
  supplierId: ID!
  roleIds: [ID!]
  routeIds: [ID!]
  archived: Boolean
}

enum SyncType {
  INVOICES
  ITEMS
  CUSTOMERS
}

enum SyncStatus {
  IN_PROGRESS
  COMPLETED
  FAILED
}

type SyncStatusItem {
  type: SyncType!
  status: String!
  lastSynced: String
  error: String
  startTime: String
  endTime: String
  durationMs: Int
  createdAt: String
  updatedAt: String
}

type SyncStatusResponse {
  supplierId: ID!
  statuses: [SyncStatusItem!]!
}

input StartSyncInput {
  supplierId: ID!
  types: [SyncType!]!
}

input ReorderCategoriesInput {
  supplierId: ID!
  categoryOrders: [ID!]!
}

input CustomerHiddenProductsInput {
  supplierId: ID!
  customerId: ID
}


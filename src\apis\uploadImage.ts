import { ApolloServer } from "@apollo/server";
import {
  S3Client,
  CompleteMultipartUploadCommandOutput,
} from "@aws-sdk/client-s3";
import { Upload } from "@aws-sdk/lib-storage";
import { Request, Response } from "express";
import mime from "mime-types";
import { v4 as uuidv4 } from "uuid";
import { awsS3Config } from "../config/environment";
import Expo from "expo-server-sdk";
import RestEndpoint from "./_restEndpoint";
import { getSupplier } from "../services/supplierService/supplierService";

export default class UploadImage extends RestEndpoint {
  private s3Client: S3Client;

  constructor(
    apolloServerInit: ApolloServer,
    expoClient: Expo,
    s3Client: S3Client
  ) {
    super(apolloServerInit, expoClient);
    this.s3Client = s3Client;
  }

  protected async worker(): Promise<void> {
    // Implementation not needed for this endpoint
  }

  public async handler(req: Request, res: Response) {
    try {
      if (!req.file) {
        throw {
          code: 400,
          message: "No image file provided",
        };
      }

      const supplierId = req.body.supplierId;
      if (!supplierId || typeof supplierId !== "string") {
        throw {
          code: 400,
          message: "supplierId is required in the form data",
        };
      }

      const supplier = await getSupplier(supplierId);
      if (!supplier) {
        throw {
          code: 400,
          message: `No supplier found with ID ${supplierId}`,
        };
      }

      let supplierName = supplier.name;
      switch (supplierName) {
        case "Whitestone Foods":
          supplierName = "whitestone";
          break;
        case "C&G Snacks":
          supplierName = "cgsnacks";
          break;
        default:
          throw {
            code: 400,
            message: `Invalid supplier name: ${supplierName}`,
          };
      }

      const file = req.file;
      // Validate file type
      if (!file.mimetype.startsWith("image/")) {
        throw {
          code: 400,
          message: "File must be an image",
        };
      }

      const fileExtension = mime.extension(file.mimetype);
      const key = `images/${supplierName}/${uuidv4()}.${fileExtension}`;

      const params = {
        Bucket: awsS3Config.bucket,
        Key: key,
        Body: file.buffer,
        ContentType: file.mimetype,
      };

      const data = (await new Upload({
        client: this.s3Client,
        params,
      }).done()) as CompleteMultipartUploadCommandOutput;

      res.status(200).json({
        url: data.Location,
      });
    } catch (err) {
      const errorMessage = `Failed to upload image: ${err.message || err}`;
      console.error(errorMessage);
      res.status(err.code || 500).send(errorMessage);
    }
  }
}

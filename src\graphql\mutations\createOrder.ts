import axios from "axios";
import knex from "../../../knex/knex";
import { MutationCreateOrderArgs } from "../../generated/graphql";
import { USDollarFromNumber } from "../../util/formats";
import { dsdSupplierConfigMap } from "../../constants/suppliers";
import { createOrder as createOrderFn } from "../../services/orderService/createOrder";
import dayjs from "dayjs";
import { postTextViaSlackWebhook } from "../../services/notificationService/slackWebhook";
import { createInvoiceFromOrder } from "../../services/invoiceService";
import { env } from "../../config/environment";

const createOrder = async (_, args: MutationCreateOrderArgs) => {
  const { userId, supplierId, orderItems, deliveryDate, ...orderFields } =
    args.createOrderInput;

  const orderDetails = await createOrderFn({
    supplierId,
    userId,
    orderItems,
    deliveryDate,
    order_name: orderFields.orderName,
    notes: orderFields.notes,
    config: orderFields.config,
  });

  await createInvoiceFromOrder(supplierId, orderDetails.id);

  const business = await knex("attain_user").where("id", userId).first();

  let slackUrl = process.env.SUBMITTED_ORDERS_SLACK_CHANNEL;

  if (dsdSupplierConfigMap[supplierId]?.orderNotificationSlackURL) {
    slackUrl = dsdSupplierConfigMap[supplierId].orderNotificationSlackURL;
  }

  if (env.production) {
    await postTextViaSlackWebhook(
      slackUrl,
      `${orderDetails.subtotal > 0 ? "Order" : "Credit Memo"} #${
        orderDetails.id
      } for ${orderDetails.supplier} created by ${
        business.name
      } for ${USDollarFromNumber(orderDetails.subtotal)}`
    );
  }
  return orderDetails;
};

export default createOrder;

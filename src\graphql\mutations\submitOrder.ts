import knex from "../../../knex/knex";
import axios from "axios";
import getCart from "../../services/cartService/getCart";
import modifyToCustomPricing from "../../services/itemService/modifyToCustomPricing";
import { MutationSubmitOrderArgs } from "../../generated/graphql";
import { sendOrderDetailsMessage } from "../../services/notificationService/sendTextMessages";
import { USDollarFromNumber } from "../../util/formats";
import { sendNotification } from "../../services/notificationService/sendNotification";
import { sendEmail } from "../../services/notificationService/sendEmail";
import addInvoice from "../../services/invoiceService/addInvoice";
import { dsdSupplierConfigMap } from "../../constants/suppliers";
import dayjs from "dayjs";
import { createInvoiceFromOrder } from "../../services/invoiceService";
import { getNextOrderNumber } from "../../services/orderService/createOrder";

const submitOrder = async (_, args: MutationSubmitOrderArgs) => {
  const {
    userId,
    cartId,
    supplier,
    notes,
    discount,
    isCredit,
    deliveryDate,
    config,
  } = args.submitOrderInput;

  const cartDetails = await getCart(cartId);

  let cartItems = cartDetails.cartItems;
  if (supplier) {
    const subCart = cartDetails.subCarts.find(
      (sub) => sub.supplier === supplier
    );
    cartItems = subCart.cartItems;
  }
  await modifyToCustomPricing(cartItems, userId);
  if (isCredit) {
    cartItems.forEach((item) => {
      item.quantity = -item.quantity;
    });
  }
  const subtotal = cartItems.reduce(
    (partialSum, a) =>
      partialSum +
      (parseFloat((a.discounted_price as never) ?? (a.price as never)) || 0.0) *
        a.quantity,
    0.0
  );

  let orderDetails, orderId, csvExists, cartCSV;

  const dateNow = new Date();
  const supplierId = (
    await knex.select("id").from("supplier").where("name", supplier).first()
  ).id;

  if (supplierId === 68 && !deliveryDate) {
    console.log("Delivery date is required for Whitestone orders");
    throw new Error("Delivery date is required");
  }

  try {
    const orderNumber = await getNextOrderNumber(supplier);

    await knex.transaction(async (trx) => {
      // Insert order and get ID
      const [orderResult] = await trx("order_detail")
        .insert({
          user_id: userId,
          status: config?.submitted_by_driver ? "In Transit" : "submitted",
          subtotal: subtotal,
          date_submitted: dateNow.toUTCString(),
          single_supplier: supplier,
          discount: discount ?? 0,
          notes,
          config,
          order_number: orderNumber,
        })
        .returning("*");
      orderDetails = orderResult;
      orderId = orderResult.id;
      // Prepare bulk insert for order items
      const orderItems = cartItems.map((item) => ({
        order_id: orderId,
        item_id: item.item_id,
        quantity: item.quantity,
        price_purchased_at: item.discounted_price ?? item.price,
      }));

      // Bulk insert order items
      await trx("order_item").insert(orderItems);

      // Delete cart items and reset cart
      const cartItemIds = cartItems.map((item) => item.id);
      await Promise.all([
        trx("cart_item").whereIn("id", cartItemIds).del(),
        trx("cart").where("id", cartId).update({ csv: "", subtotal: 0.0 }),
      ]);

      // Check for cart CSV and insert into duffl_order if exists
      cartCSV = await trx
        .select()
        .table("cart_csv")
        .where("cart_id", cartId)
        .where("supplier", supplier);

      csvExists =
        cartCSV.length > 0 &&
        typeof cartCSV[0].csv === "string" &&
        cartCSV[0].csv.length > 0;

      if (csvExists) {
        await trx("duffl_order").insert({
          csv: cartCSV[0].csv,
          order_id: orderId,
          status: "open",
          user_id: userId,
        });

        // Delete from cart_csv
        await trx("cart_csv")
          .where("cart_id", cartId)
          .where("supplier", supplier)
          .del();
      }
      const formattedDeliveryDate = deliveryDate
        ? dayjs(deliveryDate).utc().format("YYYY-MM-DD")
        : null;
      // Create Order Status
      await trx("order_status").insert({
        order_id: orderResult.id,
        supplier_id: supplierId,
        delivery_date:
          formattedDeliveryDate ??
          (supplierId.toString() !== "31"
            ? null
            : dayjs().utc().format("YYYY-MM-DD")),
      });
    });

    const business = await knex("attain_user").where("id", userId).first();

    let slackUrl = process.env.SUBMITTED_ORDERS_SLACK_CHANNEL;
    if (dsdSupplierConfigMap[supplierId]?.orderNotificationSlackURL) {
      slackUrl = dsdSupplierConfigMap[supplierId].orderNotificationSlackURL;
    }
    if (userId === "1") {
      slackUrl =
        "*********************************************************************************";
    }

    try {
      await axios.post(
        slackUrl,
        {
          text:
            `${
              subtotal > 0 ? "Order" : "Credit Memo"
            } #${orderId} for ${supplier} submitted by ${
              business.name
            } for ${USDollarFromNumber(subtotal)}` +
            (csvExists ? `: \n${cartCSV[0].csv}` : ""),
        },
        {
          headers: {
            accept: "application/json",
            "content-type": "application/json",
          },
        }
      );
    } catch (error) {
      console.log("Error sending Slack message", error);
    }

    if (
      supplier === "A&I Beverage" ||
      supplier === "A&G Wholesale" ||
      supplier === "Empire Snacks"
    ) {
      try {
        await sendOrderDetailsMessage(orderId);
      } catch (error) {
        console.log("Error sending order details message", error);
      }
    }

    if (supplier === "Beverage Express") {
      const notification = {
        title: `Order Confirmed`,
        subtitle: ``,
        body: `Your order #${orderId} has been confirmed by ${supplier}! Track your order status in the the orders tab.`,
        data: {},
      };
      try {
        await sendNotification([userId], notification);
      } catch (error) {
        console.log("Error sending notification", error);
      }
    }

    await createInvoiceFromOrder(supplierId, orderId);

    return orderDetails;
  } catch (error) {
    console.log(error);
    // Handle error, transaction rolled back
  }
};
export default submitOrder;

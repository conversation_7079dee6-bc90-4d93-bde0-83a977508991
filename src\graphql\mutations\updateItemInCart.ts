import knex from "../../../knex/knex";
import { Cart, MutationUpdateItemInCartArgs } from "../../generated/graphql";
import getCart from "../../services/cartService/getCart";

const updateItemInCart = async (_, args: MutationUpdateItemInCartArgs) => {
  const { cartId, itemId, quantity } = args.updateItemInCartInput;

  const items = await knex("cart_item")
    .select("*")
    .where("item_id", itemId)
    .where("cart_id", cartId);
  if (items.length === 0) {
    await knex("cart_item").insert({
      cart_id: cartId,
      item_id: itemId,
      quantity: quantity,
    });
  } else {
    if (quantity === 0) {
      await knex("cart_item")
        .where("item_id", itemId)
        .where("cart_id", cartId)
        .del();
    } else {
      await knex("cart_item")
        .update("quantity", quantity)
        .where("item_id", itemId)
        .where("cart_id", cartId);
    }
  }

  const cartDetails: Cart = await getCart(cartId);

  return cartDetails;
};

export default updateItemInCart;

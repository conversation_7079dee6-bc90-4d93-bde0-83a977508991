import knex from "../../../knex/knex";
import { QueryBrandSectionsArgs } from "../../generated/graphql";
import addLastOrderedDate from "../../services/itemService/addLastOrderedDate";
import { getHiddenProductIds } from "../../services/itemService";

const BrandSections = async (_, args: QueryBrandSectionsArgs) => {
  const { userId, pagination } = args.getSectionsInput;
  const { offset, limit } = pagination;

  let hiddenProductIds: string[] = [];
  if (userId) {
    const supplierInfo = await knex("supplier_times")
      .select("supplier_id")
      .where("business_id", userId)
      .first();

    if (supplierInfo) {
      hiddenProductIds = await getHiddenProductIds(
        supplierInfo.supplier_id.toString(),
        userId
      );
    }
  }

  const brands = [21, 22, 23, 24, 12, 25];
  try {
    // First, get all supplier information in one batch
    const supplierInfos = await knex
      .select()
      .table("supplier")
      .whereIn("id", brands);

    // Then, fetch items for each supplier concurrently
    const itemsPromises = supplierInfos.map(async (supplierInfo) => {
      let query = knex
        .select()
        .table("item")
        .where("supplier", supplierInfo.name)
        .limit(10);

      // Filter out hidden products if any exist
      if (hiddenProductIds.length > 0) {
        query = query.whereNotIn("id", hiddenProductIds);
      }

      const items = await query;

      return {
        ...supplierInfo,
        itemsPreview: addLastOrderedDate(items, userId),
      };
    });

    return Promise.all(itemsPromises);
  } catch (error) {
    // Handle errors here
    console.error(error);
    throw error; // or return an error response
  }
  // return [];
};

export default BrandSections;

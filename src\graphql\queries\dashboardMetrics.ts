import { Knex } from "knex";
import knex from "../../../knex/knex";
import {
  DashboardMetricType,
  DashboardMetrics as DashboardMetricsResult,
  QueryDashboardMetricsArgs,
} from "../../generated/graphql";
import { dsdSupplierConfigMap } from "../../constants/suppliers";
import { getNetRevenue } from "../../services/reportService/getNetRevenue";
import { getGMV } from "../../services/reportService/getGMV";
import { getCredits } from "../../services/reportService/getCredits";

const DashboardMetrics = async (
  _parent,
  args: QueryDashboardMetricsArgs,
  _contextValue,
  info
) => {
  const { supplier_id, duration, dateRange, routeIds, driver, serviceType } =
    args;
  const metrics: DashboardMetricsResult = {};
  const types = info.fieldNodes[0].selectionSet.selections.map(
    (s) => s.name.value
  );
  const supplierName = (
    await knex("supplier").where("id", supplier_id).select("name").first()
  ).name;

  await Promise.all(
    types.map(
      fetchMetric(
        metrics,
        supplier_id,
        supplierName,
        duration,
        dateRange,
        routeIds,
        driver,
        serviceType
      )
    )
  );
  return metrics;
};

const fetchMetric =
  (
    metrics: DashboardMetricsResult,
    supplierId: string,
    supplierName: string,
    lastNDays?: number,
    dateRange?: Date[],
    routeIds?: string[],
    driver?: string,
    serviceType?: string
  ) =>
  async (type: DashboardMetricType) => {
    const additionalFilters = async (
      tablePrefix?: string,
      excludeUsersFromDates?: boolean,
      filterTable = "order_detail"
    ) => {
      let dateField = "date_submitted";
      switch (filterTable) {
        case "invoice":
          dateField = "date_created";
          break;
        case "order_detail":
        default:
          dateField = "date_submitted";
      }
      const usersFromBeforeNDays =
        lastNDays && excludeUsersFromDates
          ? (
              await knex(filterTable)
                .where("single_supplier", supplierName)
                .whereRaw(
                  `${dateField} <= current_date - interval '${lastNDays}' day`
                )
                .distinct("user_id")
            ).map((c) => c.user_id)
          : [];
      const usersFromNotInDateRange =
        dateRange && dateRange.length === 2 && excludeUsersFromDates
          ? (
              await knex(filterTable)
                .where("single_supplier", supplierName)
                .whereNotBetween(`${dateField}`, [dateRange[0], dateRange[1]])
                .distinct("user_id")
            ).map((c) => c.user_id)
          : [];
      const tablePrefixWithDot = `${tablePrefix || filterTable}.`;

      return (queryBuilder: Knex.QueryBuilder) => {
        if (lastNDays) {
          queryBuilder.whereRaw(
            `${tablePrefixWithDot}${dateField} > current_date - interval '${lastNDays}' day`
          );
          if (excludeUsersFromDates) {
            queryBuilder.whereNotIn(
              `${tablePrefixWithDot}user_id`,
              usersFromBeforeNDays
            );
          }
        }
        if (dateRange && dateRange.length === 2) {
          queryBuilder.whereBetween(`${tablePrefixWithDot}${dateField}`, [
            dateRange[0],
            dateRange[1],
          ]);
          if (excludeUsersFromDates) {
            queryBuilder.whereNotIn(
              `${tablePrefixWithDot}user_id`,
              usersFromNotInDateRange
            );
          }
        }

        if ((routeIds && routeIds.length) || driver || serviceType) {
          queryBuilder
            .innerJoin(
              "attain_user",
              "attain_user.id",
              `${tablePrefixWithDot}user_id`
            )
            .where((builder) => {
              if (driver) {
                // First check if there's a custom_driver on the order
                builder.where((subBuilder) => {
                  subBuilder.whereRaw(
                    `${tablePrefixWithDot}config->>'custom_driver' = ?`,
                    driver
                  );
                });
                // For orders with no custom_driver, check route_ids
                builder.orWhere((subBuilder) => {
                  subBuilder
                    .whereRaw(
                      `${tablePrefixWithDot}config->>'custom_driver' IS NULL`
                    )
                    .andWhere((routeBuilder) => {
                      if (routeIds && routeIds.length) {
                        routeBuilder.where(
                          knex.raw(
                            `?::text[] && string_to_array(attain_user.route_id, ',')`,
                            [routeIds]
                          )
                        );
                      }
                    });
                });
              } else if (routeIds && routeIds.length) {
                // If using route instead of driver, check routes
                // First check if there's a custom_route on the order
                builder.where((subBuilder) => {
                  subBuilder.whereRaw(
                    `${tablePrefixWithDot}config->>'custom_route' = ANY(?::text[])`,
                    [routeIds]
                  );
                });
                // For orders with no custom_route, check route_ids
                builder.orWhere((subBuilder) => {
                  subBuilder
                    .whereRaw(
                      `${tablePrefixWithDot}config->>'custom_route' IS NULL`
                    )
                    .andWhere((routeBuilder) => {
                      if (routeIds && routeIds.length) {
                        routeBuilder.where(
                          knex.raw(
                            `?::text[] && string_to_array(attain_user.route_id, ',')`,
                            [routeIds]
                          )
                        );
                      }
                    });
                });
              }
            })
            .where((builder) => {
              if (serviceType) {
                builder.whereRaw(
                  "attain_user.config->>'service_type' = ?",
                  serviceType
                );
              }
            });
        }
      };
    };

    const supplierIsDsd =
      Object.keys(dsdSupplierConfigMap).includes(supplierId) &&
      dsdSupplierConfigMap[supplierId].dsd;

    switch (type) {
      case DashboardMetricType.ActiveBuyers:
        {
          const orderCounts: { order_count: number }[] = supplierIsDsd
            ? await knex("invoice")
                .where("supplier_id", supplierId)
                .andWhereNot("invoice.archived", true)
                .modify((queryBuilder) => {
                  if (supplierId === "31") {
                    queryBuilder.whereNotExists(function () {
                      this.select("*")
                        .from("order_detail")
                        .whereRaw("order_detail.id = invoice.order_id")
                        .where("order_detail.status", "Canceled");
                    });
                  } else {
                    queryBuilder
                      .join(
                        "order_detail",
                        "order_detail.id",
                        "invoice.order_id"
                      )
                      .whereILike("order_detail.status", "%delivered%");
                  }
                })
                .modify(
                  await additionalFilters(undefined, undefined, "invoice")
                )
                .groupBy("invoice.user_id")
                .count({ order_count: "invoice.id" })
            : await knex("order_detail")
                .where("single_supplier", supplierName)
                .andWhereNot((builder) => {
                  builder
                    .whereILike("status", "%canceled%")
                    .orWhereILike("status", "%cancelled%");
                })
                .modify(await additionalFilters())
                .groupBy("user_id")
                .count({ order_count: "order_detail.id" });
          metrics[type] = orderCounts.filter(
            (order) => order.order_count >= 1
          ).length;
        }
        break;
      case DashboardMetricType.FirstOrders:
        {
          metrics[type] = parseInt(
            (
              await knex("order_detail")
                .where("single_supplier", supplierName)
                .andWhereNot((builder) => {
                  builder
                    .whereILike("status", "%canceled%")
                    .orWhereILike("status", "%cancelled%");
                })
                .modify(await additionalFilters("", true))
                .countDistinct("user_id")
            )[0].count as string
          );
        }
        break;
      case DashboardMetricType.AvgOrderValue:
        {
          const avgOrderValueResult = supplierIsDsd
            ? await knex("invoice")
                .where("supplier_id", supplierId)
                .andWhereNot("invoice.archived", true)
                .andWhere("invoice.subtotal", ">", 0)
                .modify((queryBuilder) => {
                  if (supplierId === "31") {
                    queryBuilder.whereNotExists(function () {
                      this.select("*")
                        .from("order_detail")
                        .whereRaw("order_detail.id = invoice.order_id")
                        .where("order_detail.status", "Canceled");
                    });
                  } else {
                    queryBuilder
                      .join(
                        "order_detail",
                        "order_detail.id",
                        "invoice.order_id"
                      )
                      .whereILike("order_detail.status", "%delivered%");
                  }
                })
                .modify(
                  await additionalFilters(undefined, undefined, "invoice")
                )
                .avg("invoice.subtotal")
            : await knex("order_detail")
                .where("single_supplier", supplierName)
                .andWhereNot((builder) => {
                  builder
                    .whereILike("status", "%canceled%")
                    .orWhereILike("status", "%cancelled%");
                })
                .modify(await additionalFilters())
                .avg("subtotal");

          const avgOrderValue = parseFloat(avgOrderValueResult[0]?.avg || "0");
          metrics[type] = isNaN(avgOrderValue) ? 0 : avgOrderValue;
        }
        break;
      case DashboardMetricType.NumOrders:
        metrics[type] = parseInt(
          (
            await knex("order_detail")
              .where("single_supplier", supplierName)
              .modify(await additionalFilters())
              .count("order_detail.id")
          )[0].count as string
        );
        break;
      case DashboardMetricType.GmvTotal:
        {
          const sumGMVTotal = await getGMV(
            supplierId,
            supplierName,
            {
              lastNDays,
              dateRange,
              routeIds,
              driver,
              serviceType,
            }
          );

          metrics[type] = sumGMVTotal || 0;
        }
        break;
      case DashboardMetricType.CasesOrdered:
        {
          const sumQuantityResult = supplierIsDsd
            ? await knex("invoice")
                .where("supplier_id", supplierId)
                .andWhereNot("invoice.archived", true)
                .andWhere("invoice.subtotal", ">", 0)
                .modify((queryBuilder) => {
                  if (supplierId === "31") {
                    queryBuilder.whereNotExists(function () {
                      this.select("*")
                        .from("order_detail")
                        .whereRaw("order_detail.id = invoice.order_id")
                        .where("order_detail.status", "Canceled");
                    });
                  } else {
                    queryBuilder
                      .join(
                        "order_detail",
                        "order_detail.id",
                        "invoice.order_id"
                      )
                      .whereILike("order_detail.status", "%delivered%");
                  }
                })
                .modify(
                  await additionalFilters(undefined, undefined, "invoice")
                )
                .innerJoin(
                  "invoice_item",
                  "invoice.id",
                  "invoice_item.invoice_id"
                )
                .sum({ totalQuantity: "invoice_item.quantity" })
            : await knex("order_detail")
                .where("single_supplier", supplierName)
                .andWhereNot((builder) => {
                  builder
                    .whereILike("status", "%canceled%")
                    .orWhereILike("status", "%cancelled%");
                })
                .modify(await additionalFilters())
                .leftOuterJoin(
                  "order_item",
                  "order_detail.id",
                  "order_item.order_id"
                )
                .sum({ totalQuantity: "order_item.quantity" });

          const totalQuantity = parseInt(
            sumQuantityResult[0]?.totalQuantity || "0",
            10
          );
          metrics[type] = isNaN(totalQuantity) ? 0 : totalQuantity;
        }
        break;
      case DashboardMetricType.NumInvoices:
        metrics[type] = parseInt(
          (
            await knex("invoice")
              .where("supplier_id", supplierId)
              .modify(await additionalFilters(undefined, undefined, "invoice"))
              .count("invoice.id")
          )[0].count as string
        );
        break;
      case DashboardMetricType.NumUnconfirmedOrders:
        if (supplierId == "31") {
          metrics[type] =
            parseInt(
              (
                await knex("order_detail")
                  .join("invoice", "invoice.order_id", "=", "order_detail.id")
                  .orWhere("single_supplier", supplierName)
                  .andWhereILike("status", "%in transit%")
                  .count("order_detail.id")
              )[0].count as string
            ) +
            parseInt(
              (
                await knex("order_detail")
                  .where("single_supplier", supplierName)
                  .andWhereILike("status", "%submitted%")
                  .count("order_detail.id")
              )[0].count as string
            );
        } else {
          metrics[type] = parseInt(
            (
              await knex("order_detail")
                .where("single_supplier", supplierName)
                .andWhereILike("status", "%submitted%")
                .count("order_detail.id")
            )[0].count as string
          );
        }
        break;
      case DashboardMetricType.NumUsers:
        metrics[type] = parseInt(
          (
            await knex({ u: "attain_user" })
              .innerJoin({ st: "supplier_times" }, "u.id", "st.business_id")
              .where("st.supplier_id", supplierId)
              .count("u.id")
          )[0].count as string
        );
        break;
      case DashboardMetricType.NumCanceledOrders:
        metrics[type] = parseInt(
          (
            await knex("order_detail")
              .where("single_supplier", supplierName)
              .andWhere((builder) => {
                builder
                  .whereILike("status", "%canceled%")
                  .orWhereILike("status", "%cancelled%");
              })
              .count("order_detail.id")
          )[0].count as string
        );
        break;
      case DashboardMetricType.NumConfirmedOrders:
        metrics[type] = parseInt(
          (
            await knex("order_detail")
              .where("single_supplier", supplierName)
              .andWhere((builder) => {
                builder
                  .whereILike("status", "%in transit%")
                  .orWhereILike("status", "%delivered%");
              })
              .count("order_detail.id")
          )[0].count as string
        );
        break;
      case DashboardMetricType.BrandBreakdown:
        {
          const brandTotals = await knex({ od: "order_detail" })
            .select("i.brand")
            .innerJoin({ oi: "order_item" }, "od.id", "oi.order_id")
            .innerJoin({ i: "item" }, "oi.item_id", "i.id")
            .where("od.single_supplier", supplierName)
            .andWhereNot((builder) => {
              builder
                .whereILike("status", "%canceled%")
                .orWhereILike("status", "%cancelled%");
            })
            .modify(await additionalFilters("od"))
            .groupBy("i.brand")
            .sum({ total_count: "oi.quantity" })
            .orderBy("total_count", "desc");
          const total = brandTotals
            .map((metric) => parseFloat(metric.total_count))
            .reduce((totalQuantity, quantity) => totalQuantity + quantity, 0);

          metrics[type] = brandTotals.map((metric) => ({
            brand: metric.brand,
            quantity: parseInt(metric.total_count),
            breakdown: parseFloat(metric.total_count) / total,
          }));
        }
        break;
      case DashboardMetricType.BestSellers:
        {
          const itemTotals = await knex({ od: "order_detail" })
            .select("i.name", "i.image")
            .innerJoin({ oi: "order_item" }, "od.id", "oi.order_id")
            .innerJoin({ i: "item" }, "oi.item_id", "i.id")
            .where("od.single_supplier", supplierName)
            .andWhereNot((builder) => {
              builder
                .whereILike("status", "%canceled%")
                .orWhereILike("status", "%cancelled%");
            })
            .modify(await additionalFilters("od"))
            .groupBy("i.id", "i.name", "i.image")
            .sum({ quantity: "oi.quantity" })
            .orderBy("quantity", "desc")
            .limit(50);
          metrics[type] = itemTotals.map((item) => ({
            name: item.name,
            image: item.image,
            quantity: parseInt(item.quantity),
          }));
        }
        break;
      case DashboardMetricType.NumUnapprovedCustomers:
        metrics[type] = parseInt(
          (
            await knex({ u: "attain_user" })
              .innerJoin({ st: "supplier_times" }, "u.id", "st.business_id")
              .andWhere("st.supplier_id", supplierId)
              .andWhere("u.approved", false)
              .andWhereNot("u.archived", true)
              .count("u.id")
          )[0].count as string
        );
        break;
      case DashboardMetricType.ProfitTotal:
        {
          const sumOrderProfitResult = await knex("invoice")
            .where("supplier_id", supplierId)
            .andWhereNot("invoice.archived", true)
            .modify((queryBuilder) => {
              if (supplierId === "31") {
                queryBuilder.whereNotExists(function () {
                  this.select("*")
                    .from("order_detail")
                    .whereRaw("order_detail.id = invoice.order_id")
                    .where("order_detail.status", "Canceled");
                });
              } else {
                queryBuilder
                  .join("order_detail", "order_detail.id", "invoice.order_id")
                  .whereILike("order_detail.status", "%delivered%");
              }
            })
            .modify(await additionalFilters(undefined, undefined, "invoice"))
            .innerJoin("invoice_item", "invoice.id", "invoice_item.invoice_id")
            .modify((queryBuilder) => {
              if (supplierId === "31") {
                queryBuilder.whereRaw(
                  "(invoice_item.price - invoice_item.cog_price) > 0"
                );
              }
            })
            .sum(
              knex.raw(
                "invoice_item.quantity * (invoice_item.price - invoice_item.cog_price)"
              )
            );

          const sumOrderProfit = parseFloat(
            sumOrderProfitResult[0]?.sum || "0"
          );
          metrics[type] = isNaN(sumOrderProfit) ? 0 : sumOrderProfit;
        }
        break;
      case DashboardMetricType.CreditTotal:
        {
          const creditsTotal = await getCredits(
            supplierId,
            supplierName,
            {
              lastNDays,
              dateRange,
              routeIds,
              driver,
              serviceType,
            }
          );
          metrics[type] = creditsTotal || 0;
        }
        break;
      case DashboardMetricType.NetRevenue:
        {
          const netRevenue = await getNetRevenue(
            supplierId,
            supplierName,
            {
              lastNDays,
              dateRange,
              routeIds,
              driver,
              serviceType,
            }
          );
          metrics[type] = netRevenue;
        }
        break;
    }
  };

export default DashboardMetrics;

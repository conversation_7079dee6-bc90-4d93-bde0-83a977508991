import knex from "../../../knex/knex";
import { GraphQLError } from "graphql";
import { QueryInvoiceItemsArgs } from "../../generated/graphql";

const invoiceItems = async (_, args: QueryInvoiceItemsArgs) => {
  const { orderId, invoiceId } = args;
  if (orderId && invoiceId) {
    throw new GraphQLError("Only one of orderId or invoiceId is required", {
      extensions: { code: "BAD_USER_INPUT" },
    });
  }
  if (invoiceId)
    return await knex("invoice_item")
      .select("*")
      .where("invoice_id", invoiceId)
      .orderBy("id", "asc");
  if (orderId)
    return await knex
      .queryBuilder()
      .select("invoice_item.*")
      .from("invoice_item")
      .join("invoice", "invoice.id", "=", "invoice_item.invoice_id")
      .where("invoice.order_id", orderId)
      .orderBy("id", "asc");

  throw new GraphQLError("One of orderId or invoiceId is required", {
    extensions: { code: "BAD_USER_INPUT" },
  });
};

export default invoiceItems;

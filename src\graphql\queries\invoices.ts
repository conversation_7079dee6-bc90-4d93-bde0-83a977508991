import knex from "../../../knex/knex";
import { QueryInvoicesArgs } from "../../generated/graphql";

const Invoices = async (_, args: QueryInvoicesArgs) => {
  // TODO: use userId to fetch encrypted scraper credentials and decrypt them
  const { ids, supplierId, query, orderIds, pagination, onlyWithCredits } =
    args.getInvoicesInput;
  const { offset, limit } = pagination || {};
  const invoicesQuery = knex("invoice")
    .select(
      "invoice.*", // Select all columns from invoice
      // Build a JSON object for user data
      knex.raw(
        `json_build_object('id', "attain_user".id, 'name', "attain_user".name, 'email', "attain_user".email, 'contact_email', "attain_user".contact_email, 'phone_number', "attain_user".phone_number, 'address', "attain_user".address, 'route_id', "attain_user".route_id) as "customerDetails"`
      )
    )
    .leftJoin("attain_user", "invoice.user_id", "attain_user.id") // Adjust 'users' to your actual user table name
    .offset(offset)
    .limit(limit)
    .orderBy("date_created", "desc")
    .orderBy("id", "desc");

  if (supplierId) {
    invoicesQuery
      .where("invoice.supplier_id", supplierId)
      .whereNot("invoice.archived", true);
  }

  if (ids) {
    invoicesQuery.where("invoice.id", ids[0]);
  }

  if (orderIds && orderIds.length > 0) {
    invoicesQuery.whereIn("invoice.order_id", orderIds);
  }

  if (query) {
    invoicesQuery.where("attain_user.name", "ilike", `%${query}%`);
  }

  if (onlyWithCredits) {
    invoicesQuery.where("invoice.credit", ">", 0);
  }
  const invoices = await invoicesQuery;

  for (let i = 0; i < invoices.length; i++) {
    const invoice = invoices[i];
    const invoiceItems = await knex("invoice_item")
      .select("invoice_item.*", "item.metadata")
      .leftJoin("item", "invoice_item.item_id", "item.id")
      .where("invoice_id", invoice.id)
      .orderBy("id", "asc");
    const orderDetails = await knex("order_detail")
      .select("*")
      .where("id", invoice.order_id);
    const order_delivery_date = await knex("order_status")
      .select("delivery_date")
      .where("order_id", invoice.order_id)
      .first();
    invoice["invoiceItems"] = invoiceItems;
    invoice["orderDetails"] = {
      ...orderDetails[0],
      delivery_date: order_delivery_date?.delivery_date,
    };
  }

  return invoices;
};

export default Invoices;

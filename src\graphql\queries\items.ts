import knex from "../../../knex/knex";
import { filterByUpc, logUpc, modifyUpc } from "../../util/upcs";
import addLastOrderedDate from "../../services/itemService/addLastOrderedDate";
import { QueryItemsArgs } from "../../generated/graphql";
import modifyToCustomPricing from "../../services/itemService/modifyToCustomPricing";
import { getHiddenProductIds } from "../../services/itemService";

const Items = async (_, args: QueryItemsArgs) => {
  const { userId, ids, upcs, pagination } = args.getItemsInput;
  const { offset, limit } = pagination;

  // Get hidden product IDs if userId is provided
  let hiddenProductIds: string[] = [];
  if (userId) {
    // Get supplier ID from supplier_times table
    const supplierInfo = await knex("supplier_times")
      .select("supplier_id")
      .where("business_id", userId)
      .first();

    if (supplierInfo) {
      hiddenProductIds = await getHiddenProductIds(
        supplierInfo.supplier_id.toString(),
        userId
      );
    }
  }

  if (!upcs && !ids) {
    let items;
    if (userId === "1") {
      let query = knex
        .select()
        .table("item")
        .where("supplier", "Pitco Foods")
        .where("archived", false)
        .whereNotIn("nacs_category", [
          "TOBACCO - TAXABLE",
          "TOBACCO ACCESSORIES - TAXABLE",
          "CIGARETTES - TAXABLE",
          "ALCOHOL - TAXABLE",
        ]);

      // Filter out hidden products if any exist
      if (hiddenProductIds.length > 0) {
        query = query.whereNotIn("id", hiddenProductIds);
      }

      items = await query.limit(limit).offset(offset).orderBy("name", "asc");
    } else {
      let query = knex
        .select()
        .table("item")
        .where("supplier", "Pitco Foods")
        .where("local_item", false)
        .where("outdated", false)
        .where("archived", false);

      // Filter out hidden products if any exist
      if (hiddenProductIds.length > 0) {
        query = query.whereNotIn("id", hiddenProductIds);
      }

      items = await query.limit(limit).offset(offset).orderBy("name", "asc");
    }
    await modifyToCustomPricing(items, userId);
    return addLastOrderedDate(items, userId);
  }
  if (ids) {
    let query = knex.select().table("item").whereIn("id", ids);

    // Filter out hidden products if any exist
    if (hiddenProductIds.length > 0) {
      query = query.whereNotIn("id", hiddenProductIds);
    }

    const items = await query;
    const item = items[0];
    const relatedItems = await knex
      .select()
      .table("item")
      .where("supplier", item.supplier)
      .where("nacs_category", item.nacs_category)
      .andWhere("nacs_subcategory", item.nacs_subcategory)
      .whereNotIn("id", ids)
      .where("archived", false)
      .limit(10);
    const temp = item;
    temp["related_items"] = relatedItems;
    await modifyToCustomPricing([temp], userId);
    return addLastOrderedDate([temp], userId);
  }
  if (upcs) {
    const modifiedUpc = modifyUpc(upcs[0]);
    const suppliers = await knex
      .select()
      .table("supplier_times")
      .join("supplier", "supplier_times.supplier_id", "supplier.id")
      .where("supplier_times.business_id", userId)
      .orderBy("supplier.id", "asc");

    let query = knex("item")
      .select("item.*", knex.raw("json_agg(supplier.*)->0 as supplier_info"))
      .whereIn(
        "supplier",
        suppliers.map((supplier) => supplier.name)
      )
      .where("local_item", false)
      .where("outdated", false)
      .where("archived", false)
      .where((whereBuilder) => filterByUpc(whereBuilder, modifiedUpc))
      .leftJoin("supplier", "item.supplier", "supplier.name")
      .groupBy("item.id");

    // Filter out hidden products if any exist
    if (hiddenProductIds.length > 0) {
      query = query.whereNotIn("item.id", hiddenProductIds);
    }

    const result = await query;
    const user = await knex("attain_user").where("id", userId);
    await logUpc(user[0].name, userId, "General", upcs[0], modifiedUpc, result);
    await modifyToCustomPricing(result, userId);
    return addLastOrderedDate(result, userId);
  }
};

export default Items;

import knex from "../../../knex/knex";
import { filterByUpc, logUpc, modifyUpc } from "../../util/upcs";
import addLastOrderedDate from "../../services/itemService/addLastOrderedDate";
import { QueryItemsBySupplierArgs } from "../../generated/graphql";
import modifyToCustomPricing from "../../services/itemService/modifyToCustomPricing";
import { getHiddenProductIds } from "../../services/itemService";

const DEFAULT_SUPPLIER = "Pitco Foods";
const TEST_ACC_BLOCKED = [
  "TOBACCO - TAXABLE",
  "TOBACCO ACCESSORIES - TAXABLE",
  "CIGARETTES - TAXABLE",
  "ALCOHOL - TAXABLE",
];

const ItemsBySupplier = async (_, args: QueryItemsBySupplierArgs) => {
  const {
    userId,
    supplierId,
    upc,
    pagination,
    query,
    brand,
    category,
    section,
    ids,
  } = args.getItemsBySupplierInput;

  const supplierMatch = await knex
    .select()
    .table("supplier")
    .where("id", supplierId);
  const supplier =
    supplierMatch.length === 0 ? DEFAULT_SUPPLIER : supplierMatch[0].name;

  let modifiedUpc;

  // Get hidden product IDs if userId is provided
  let hiddenProductIds: string[] = [];
  if (userId) {
    hiddenProductIds = await getHiddenProductIds(supplierId, userId);
  }

  const itemsQuery = knex
    .select()
    .table("item")
    .where("supplier", supplier)
    .where("local_item", false)
    .where("outdated", false)
    .whereNot("archived", true)
    .andWhere((whereBuilder) => {
      if (upc) {
        modifiedUpc = modifyUpc(upc);
        return filterByUpc(whereBuilder, modifiedUpc);
      }

      // if (userId) {
      //   return whereBuilder
      //     .whereNotIn("nacs_category", userId === "1" ? TEST_ACC_BLOCKED : [])
      //     .orWhereNull("nacs_category");
      // }

      if (query && query.length > 0) {
        return whereBuilder
          .where("name", "ilike", `%${query}%`)
          .orWhere("nacs_category", "ilike", `%${query}%`)
          .orWhere("nacs_subcategory", "ilike", `%${query}%`)
          .orWhere("upc1", "ilike", `%${query}%`)
          .orWhere("upc2", "ilike", `%${query}%`)
          .orWhere("metadata", "ilike", `%${query}%`);
      }
      if (brand) {
        return whereBuilder
          .where("brand", "ilike", `%${brand}%`)
          .orWhere("parent_brand", "ilike", `%${brand}%`);
      }

      if (ids) {
        return whereBuilder.whereIn("id", ids);
      }
    });

  // Filter out hidden products if any exist
  if (hiddenProductIds.length > 0) {
    itemsQuery.whereNotIn("id", hiddenProductIds);
  }

  itemsQuery
    .limit(pagination ? pagination.limit : 20)
    .offset(pagination ? pagination.offset : 0)
    .orderBy("name", "asc");

  if (category) {
    itemsQuery.where((whereBuilder) =>
      whereBuilder
        .whereRaw("? ilike ANY(string_to_array(nacs_category, '//'))", category)
        .orWhereRaw(
          "? ilike ANY(string_to_array(nacs_subcategory, '//'))",
          category
        )
    );
  }

  if (section) {
    if (section === "order_again") {
      const ordered_items = await knex
        .select("order_item.item_id")
        .sum({ sum: "order_item.quantity" })
        .from("order_detail")
        .join("order_item", "order_item.order_id", "order_detail.id")
        .where("user_id", userId)
        .groupBy("order_item.item_id")
        .orderBy("sum", "desc")
        .limit(pagination ? pagination.limit : 20)
        .offset(pagination ? pagination.offset : 0);

      itemsQuery.where((whereBuilder) =>
        whereBuilder.whereIn(
          "id",
          ordered_items.map((item) => item.item_id)
        )
      );
    }
  }
  const items = await itemsQuery;

  if (userId) {
    await modifyToCustomPricing(items, userId);
  }

  if (upc) {
    const user = await knex("attain_user").where("id", userId);
    await logUpc(user[0].name, userId, supplier, upc, modifiedUpc, items);
  }

  if (!userId) return items;

  return addLastOrderedDate(items, userId);
};

export default ItemsBySupplier;

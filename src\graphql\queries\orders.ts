import knex from "../../../knex/knex";
import { QueryOrdersArgs } from "../../generated/graphql";
import getOrders from "../../services/orderService/getOrder";

const STATUSES = ["Submitted", "In Transit", "Delivered", "Canceled"];

const Orders = async (_, args: QueryOrdersArgs) => {
  const {
    ids,
    userId,
    pagination = { offset: 0, limit: 5 },
    query,
    status,
    supplierId,
  } = args.getOrdersInput;
  const { offset, limit } = pagination;

  const filter = (queryBuilder) => {
    let mappedStatuses = [];
    if (status) {
      switch (status.toLowerCase()) {
        case "confirmed": {
          mappedStatuses = ["In Transit", "Delivered"];
          break;
        }
        case "awaiting confirmation": {
          mappedStatuses = ["Submitted"];
          break;
        }
        default: {
          mappedStatuses = [status];
          break;
        }
      }
    }
    if (query && supplierId) {
      const trimmedQuery = query.trim();
      queryBuilder.andWhere((builder) => {
        builder.whereILike("attain_user.name", `%${trimmedQuery}%`);
      });
    } else if (query) {
      const trimmedQuery = query.trim();
      queryBuilder.andWhere((builder) => {
        builder
          .whereILike("order_name", `%${trimmedQuery}%`)
          .orWhereILike("status", `%${trimmedQuery}%`);

        const digits = trimmedQuery.match(/\d+/);
        if (digits) {
          builder.orWhere("id", parseInt(digits[0]));
        }
      });
    }

    if (mappedStatuses.length > 0) {
      queryBuilder.andWhere((builder) => {
        mappedStatuses.forEach((mappedStatus) => {
          if (STATUSES.includes(mappedStatus)) {
            builder
              .orWhere("status", mappedStatus)
              .orWhere("status", mappedStatus.toLowerCase());
          }
        });
      });
    }
  };

  let baseQuery;

  if (userId) {
    baseQuery = knex
      .select("*", "order_detail.id", "order_detail.config")
      .from("order_status")
      .rightOuterJoin(
        "order_detail",
        "order_status.order_id",
        "order_detail.id"
      )
      .leftOuterJoin(
        "supplier",
        "order_detail.single_supplier",
        "supplier.name"
      )
      .where("user_id", userId)
      .modify(filter)
      // .orderBy("order_detail.id", "desc")
      .orderBy("order_status.delivery_date", "desc");
  }
  if (supplierId) {
    const supplier_name = await knex
      .select("name")
      .from("supplier")
      .where("id", supplierId);
    baseQuery = knex
      .select(
        "order_status.*",
        "order_detail.*",
        "supplier.*",
        "order_detail.id as id",
        "order_detail.config as config",
        knex.raw(
          `json_build_object('id', "attain_user".id, 'name', "attain_user".name, 'email', "attain_user".email, 'address', "attain_user".address, 'route_id', "attain_user".route_id, 'custom_prices', coalesce("cp".custom_prices, '[]'::json)) as "customerDetails"`
        )
      )
      .from("order_status")
      .rightOuterJoin(
        "order_detail",
        "order_status.order_id",
        "order_detail.id"
      )
      .leftOuterJoin(
        "supplier",
        "order_detail.single_supplier",
        "supplier.name"
      )
      .leftOuterJoin("attain_user", "order_detail.user_id", "attain_user.id")
      .leftOuterJoin(
        knex.raw(
          "(SELECT user_id, json_agg(json_build_object('item_id', item_id, 'price', price)) as custom_prices FROM custom_item_price GROUP BY user_id) cp USING(user_id)"
        )
      )
      .where("single_supplier", supplier_name[0].name)
      .modify(filter)
      // .orderBy("order_detail.id", "desc")
      .orderBy("order_status.delivery_date", "desc");
  }

  if (!ids) {
    baseQuery = baseQuery.limit(limit).offset(offset);
  } else {
    baseQuery = baseQuery.whereIn("order_detail.id", ids);
  }

  const orders = await baseQuery;

  // Use Promise.all to fetch all order details in parallel

  const result = await getOrders(orders);

  return result;
};

export default Orders;

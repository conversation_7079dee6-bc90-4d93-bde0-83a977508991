import { QueryRecommendationsArgs } from "../../generated/graphql";
import getItemsDetails from "../../services/itemService/getItemsDetails";
import getSuppliers from "../../services/supplierService/getSuppliers";
import { getRecommendedItemsForStore } from "../../services/itemService/getRecommendedItemsForStore";
import { getHiddenProductIds } from "../../services/itemService";
import knex from "../../../knex/knex";

const Recommendations = async (_, args: QueryRecommendationsArgs) => {
  const { getRecommendationsInput } = args;
  const { userId, limit } = getRecommendationsInput;

  let hiddenProductIds: string[] = [];
  if (userId) {
    const supplierInfo = await knex("supplier_times")
      .select("supplier_id")
      .where("business_id", userId)
      .first();

    if (supplierInfo) {
      hiddenProductIds = await getHiddenProductIds(
        supplierInfo.supplier_id.toString(),
        userId
      );
    }
  }

  const recommendations = await getRecommendedItemsForStore(userId);

  // Filter out hidden products from recommendations before fetching details
  const filteredRecommendations = recommendations.filter(
    (recommendation) =>
      !hiddenProductIds.includes(recommendation.item_id.toString())
  );

  const supplierNames = await getSuppliers(userId);
  const recommendedItems = await getItemsDetails(
    filteredRecommendations,
    supplierNames
  );

  filteredRecommendations.forEach((recommendation) => {
    recommendation.item =
      recommendedItems.find((item) => item.id === recommendation.item_id) ||
      null;
  });

  return filteredRecommendations
    .filter((recommendation) => recommendation.item)
    .slice(0, limit);
};

export default Recommendations;

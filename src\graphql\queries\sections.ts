import { QuerySectionsArgs, Section } from "../../generated/graphql";
import getItemsBySection from "../../services/itemService/getItemsBySection";

const sectionMap = {
  new_items: "New Items",
  on_sale: "On Sale",
  order_again: "Order Again",
  top_sellers: "Top Sellers",
  recommended: "Recommended For You",
  back_in_stock: "Back In Stock",
};

const Sections = async (_, args: QuerySectionsArgs): Promise<Section[]> => {
  const { userId, sections, pagination } = args.getSectionsInput;
  const { offset = 0, limit = 10 } = pagination || {};

  // Process each userId
  const sectionResults: Section[] = await Promise.all(
    (sections ? sections : Object.keys(sectionMap)).map(async (section) => {
      return {
        name: sectionMap[section],
        value: section,
        items: await getItemsBySection(userId, section, offset, limit),
      };
    })
  );

  return sectionResults.filter((section) => section.items.length > 0);
};

export default Sections;

import knex from "../../../knex/knex";
import { QuerySpotlightsArgs } from "../../generated/graphql";
const Spotlights = async (_, args: QuerySpotlightsArgs) => {
  const { userId } = args;
  // const { offset, limit } = pagination;

  const spotlightsQuery = knex
    .select("*")
    .from("spotlight")
    .join("supplier", "spotlight.supplier_id", "supplier.id")
    .orderBy("spotlight.id", "desc")
    .where("spotlight.id", "!=", "8");

  if (userId) {
    const suppliers = await knex("supplier_times")
      .select("*")
      .where("business_id", userId);
    const supplierIds = suppliers.map((supplier) => supplier.supplier_id);
    if (supplierIds.includes(30)) {
      spotlightsQuery.whereIn("spotlight.id", [1, 6, 7]);
    }
    if (supplierIds.includes(45)) {
      spotlightsQuery.whereIn("spotlight.id", [1, 6, 7]);
    }
    if (supplierIds.includes(46)) {
      spotlightsQuery.whereIn("spotlight.id", [1, 6, 7]);
    }
    if (supplierIds.includes(47)) {
      spotlightsQuery.whereIn("spotlight.id", [1, 6, 7]);
    }
    if (supplierIds.includes(53)) {
      spotlightsQuery.whereIn("spotlight.id", [8, 1, 2, 3, 4, 5]);
    }
    if (supplierIds.includes(57)) {
      spotlightsQuery.whereIn("spotlight.id", [1, 2, 3, 4, 5]);
    }
    if (supplierIds.includes(60)) {
      spotlightsQuery.whereIn("spotlight.id", [6, 7, 8]);
    }
    if (userId === "223") {
      spotlightsQuery.whereIn("spotlight.id", [8, 1, 2, 3, 4, 5]);
    }
  }

  const spotlights = await spotlightsQuery;
  return spotlights;
};

export default Spotlights;

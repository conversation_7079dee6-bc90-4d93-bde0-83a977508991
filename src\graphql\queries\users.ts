import knex from "../../../knex/knex";
import { QueryUsersArgs, User } from "../../generated/graphql";
import {
  populateUsersCustomPrices,
  populateDeliveryWindows,
} from "../../services/userService/userService";
import getUsersSuppliers from "../../services/userService/getUsersSuppliers";

const Users = async (_, args: QueryUsersArgs) => {
  const { ids, supplierId, pagination, includeCustomPrices } =
    args.getUsersInput;
  const { offset, limit } = pagination ?? {};
  let users: User[];

  if (supplierId) {
    users = await knex({ u: "attain_user" })
      .select("u.*")
      .distinctOn("u.id")
      .innerJoin({ st: "supplier_times" }, "u.id", "st.business_id")
      .where("st.supplier_id", supplierId)
      .where("u.supplier_beta", true)
      .whereNot("archived", true)
      .limit(limit)
      .offset(offset);
  }
  if (ids) {
    users = await knex.select().table("attain_user").whereIn("id", ids);
  }
  await getUsersSuppliers(users);
  if (includeCustomPrices) {
    await populateUsersCustomPrices(users);
  }

  // Populate delivery windows for all users
  await populateDeliveryWindows(users);

  return users;
};
export default Users;

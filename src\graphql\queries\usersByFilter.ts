import knex from "../../../knex/knex";
import {
  QueryUsersByFilterArgs,
  User,
  Ordering,
} from "../../generated/graphql";
import {
  populateUsersCustomPrices,
  populateDeliveryWindows,
} from "../../services/userService/userService";
import getUsersSuppliers from "../../services/userService/getUsersSuppliers";

// Function to calculate unpaid balance for users
const calculateUnpaidBalances = async (users: User[]) => {
  const userIds = users.map((user) => user.id);

  const invoiceBalances = await knex("invoice")
    .select("user_id")
    .select(
      knex.raw(
        "sum(COALESCE(subtotal, 0) - COALESCE(paid, 0) - COALESCE(credit, 0)) as unpaid_balance"
      )
    )
    .whereIn("user_id", userIds)
    .whereNot("archived", true)
    .whereRaw(
      "ABS(COALESCE(subtotal, 0) - COALESCE(paid, 0) - COALESCE(credit, 0)) > 1e-10"
    )
    .groupBy("user_id");

  const balanceMap = new Map();
  invoiceBalances.forEach((row) => {
    balanceMap.set(row.user_id, parseFloat(row.unpaid_balance) || 0);
  });

  // Add unpaid_balance to each user
  users.forEach((user) => {
    user.unpaid_balance = balanceMap.get(user.id) || 0;
  });
};

const UsersByFilter = async (_, args: QueryUsersByFilterArgs) => {
  const {
    supplierId,
    filters,
    sortBy: { field: sortField, ordering: sortOrdering } = {
      field: "name",
      ordering: Ordering.Asc,
    },
    pagination: { offset, limit } = { offset: 0, limit: 100 },
    includeCustomPrices,
  } = args.getUsersByFilterInput;

  // Check if we need to sort by unpaid balance or filter by hasUnpaidBalance
  const needsUnpaidBalanceCalculation =
    sortField === "unpaid_balance" || filters?.hasUnpaidBalance !== undefined;

  let query = knex({ u: "attain_user" })
    .select("u.*")
    .innerJoin({ st: "supplier_times" }, "u.id", "st.business_id")
    .where("st.supplier_id", supplierId)
    .where("u.supplier_beta", true)
    .whereNot("archived", true);

  // Add unpaid balance calculation to query if needed for sorting or filtering
  if (needsUnpaidBalanceCalculation) {
    query = query
      .leftJoin(
        knex("invoice")
          .select("user_id")
          .select(
            knex.raw(
              "sum(COALESCE(subtotal, 0) - COALESCE(paid, 0) - COALESCE(credit, 0)) as unpaid_balance"
            )
          )
          .whereNot("archived", true)
          .whereRaw(
            "ABS(COALESCE(subtotal, 0) - COALESCE(paid, 0) - COALESCE(credit, 0)) > 1e-10"
          )
          .groupBy("user_id")
          .as("ub"),
        "u.id",
        "ub.user_id"
      )
      .select(knex.raw("COALESCE(ub.unpaid_balance, 0) as unpaid_balance"));
  }

  if (filters?.searchTerm && filters.searchTerm.length > 0) {
    query = query.where((builder) => {
      builder
        .where("u.name", "ilike", `%${filters.searchTerm}%`)
        .orWhere("u.address", "ilike", `%${filters.searchTerm}%`)
        .orWhere("u.store_group", "ilike", `%${filters.searchTerm}%`);
    });
  }
  if (filters?.route && filters.route !== "all") {
    if (filters.route === "unassigned") {
      query = query.whereRaw("COALESCE(u.route_id, '') = ''");
    } else {
      query = query.where("u.route_id", filters.route);
    }
  }

  if (filters?.active && filters.active !== "all") {
    const isFilteringForActive = filters.active === "Yes";
    query = query.whereRaw(
      `
      CASE 
        WHEN config IS NULL THEN ?
        WHEN config->>'active' IS NULL THEN ?
        ELSE (config->>'active')::boolean = ?
      END
    `,
      [isFilteringForActive, isFilteringForActive, isFilteringForActive]
    );
  }

  if (filters?.driver && filters.driver !== "all_drivers_default") {
    const routeIds = await knex("route")
      .select("id")
      .where("supplier_id", supplierId)
      .where("driver", filters.driver);

    query = query.where((builder) => {
      builder.whereIn(
        "u.route_id",
        routeIds.map((route) => route.id)
      );
    });
  }

  // Filter by hasUnpaidBalance if specified
  if (filters?.hasUnpaidBalance !== undefined) {
    if (filters.hasUnpaidBalance) {
      // Only users with unpaid balance > 0
      query = query.whereRaw("COALESCE(ub.unpaid_balance, 0) > 1e-10");
    } else {
      // Only users with no unpaid balance (= 0)
      query = query.whereRaw("COALESCE(ub.unpaid_balance, 0) = 0");
    }
  }

  // Sort fields - handle unpaid_balance and case-insensitive name sorting
  if (sortField === "unpaid_balance") {
    query = query.orderBy("unpaid_balance", sortOrdering);
  } else if (sortField === "name") {
    query = query.orderByRaw(`LOWER(u.name) ${sortOrdering}`);
  } else {
    query = query.orderBy(`u.${sortField}`, sortOrdering);
  }

  const total = await query;
  if (offset || limit) {
    query = query.offset(offset).limit(limit);
  }

  const users = await query;
  await getUsersSuppliers(users);
  if (includeCustomPrices) {
    await populateUsersCustomPrices(users);
  }

  // Populate delivery windows for all users
  await populateDeliveryWindows(users);

  // Calculate unpaid balances for all users if not already done in query
  if (!needsUnpaidBalanceCalculation) {
    await calculateUnpaidBalances(users);
  } else {
    // If we already calculated in query, just ensure the field is properly set
    users.forEach((user) => {
      if (!user.unpaid_balance) {
        user.unpaid_balance = 0;
      }
    });
  }

  return { users, totalCount: total.length };
};
export default UsersByFilter;

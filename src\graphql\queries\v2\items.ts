import { QueryItemsV2Args } from "../../../generated/graphql";
import { getItems } from "../../../services/itemService/itemService";

const ItemsV2 = async (_, args: QueryItemsV2Args) => {
  const { supplierId, userId, filters, pagination, sortBy } = args.itemsInput;

  const result = await getItems({
    supplierId,
    userId,
    filters,
    pagination,
    sortBy,
  });

  return result;
};

export default ItemsV2;

import dayjs from "dayjs";
import knex from "../../../knex/knex";
import { env } from "../../config/environment";
import { AddInvoiceInput, Invoice } from "../../generated/graphql";
import addInvoiceItems from "../invoiceService/addInvoiceItems";
import { sendNotification } from "../notificationService/sendNotification";

const addInvoice = async (addInvoiceInput: AddInvoiceInput) => {
  const { invoice_items, return_items, ...invoiceData } = addInvoiceInput;
  const dateNow = new Date();

  const trxProvider = knex.transactionProvider();
  const trx = await trxProvider();

  const invoice: Invoice = await trx("invoice")
    .insert({
      ...invoiceData,
      date_created: invoiceData.date_created
        ? new Date(invoiceData.date_created).toISOString()
        : null,
      archived: false,
      updated_at: dateNow.toISOString(),
    })
    .returning("*")
    .then(async (insertedInvoice: Invoice[]) => {
      if (invoice_items && invoice_items.length > 0) {
        const invoiceItems = await addInvoiceItems(
          insertedInvoice[0].id,
          invoice_items,
          return_items ?? false,
          trxProvider
        );
        if (return_items) insertedInvoice[0].invoiceItems = invoiceItems;
      }

      // TODO: Remove this once we have a better way to handle order notifications
      if (invoiceData.order_id && invoiceData.order_id.length > 0) {
        const orderDetail = await (await trxProvider())("order_detail")
          .select("order_detail.*", "order_status.delivery_date")
          .innerJoin("order_status", "order_detail.id", "order_status.order_id")
          .where("order_detail.id", invoiceData.order_id)
          .first();

        if (
          env.production &&
          invoiceData.supplier_id !== "68" &&
          invoiceData.supplier_id !== "31"
        ) {
          const notification = {
            title: `Order Confirmed`,
            subtitle: ``,
            body: `Your order #${orderDetail.id} has been confirmed by ${orderDetail.single_supplier}! Track your order status in the the orders tab.`,
            data: {},
          };
          try {
            await sendNotification([invoiceData.user_id], notification);
          } catch (error) {
            console.log("Error sending notification", error);
          }
        }
      }
      (await trxProvider()).commit();
      return insertedInvoice[0];
    })
    .catch(async (err) => {
      console.error("Error adding invoice:", err);
      (await trxProvider()).rollback();
      throw new Error(`Error adding invoice: ${err.message}`);
    });
  return invoice;
};

export default addInvoice;

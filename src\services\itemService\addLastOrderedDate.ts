import knex from "../../../knex/knex";
import { Item } from "../../generated/graphql";
import dayjs from "dayjs";

const addLastOrderedDate = async (items: Item[], userId: string) => {
  // Get list of all item IDs
  const itemIds = items.map((item) => item.id);

  // Get supplier_id from supplier_times where userId = business_id
  const supplierInfo = await knex
    .select("supplier_id")
    .from("supplier_times")
    .where("business_id", userId)
    .first();

  const supplierId = supplierInfo ? supplierInfo.supplier_id : null;

  // Fetch all orders for these items to calculate averages and first/last order dates
  const orderHistory = await knex
    .select(
      "order_item.item_id",
      "order_detail.date_submitted",
      knex.raw("COALESCE(order_item.quantity, 0) as quantity")
    )
    .from("order_item")
    .join("order_detail", "order_item.order_id", "order_detail.id")
    .whereIn("order_item.item_id", itemIds)
    .where("order_detail.user_id", userId)
    .whereNot("order_detail.status", "Canceled")
    .orderBy("order_detail.date_submitted", "asc");

  // Create a map to store item statistics
  const itemStats = {};

  // Process each order to calculate statistics
  orderHistory.forEach((order) => {
    if (!itemStats[order.item_id]) {
      itemStats[order.item_id] = {
        first_ordered_date: order.date_submitted,
        last_ordered_date: order.date_submitted,
        total_quantity: 0,
        total_orders: 0,
      };
    }

    const stats = itemStats[order.item_id];
    stats.last_ordered_date = order.date_submitted;
    stats.total_quantity += Number(order.quantity);
    stats.total_orders += 1;
  });

  const ordersByDate = orderHistory.reduce((acc, order) => {
    const dateKey = dayjs(order.date_submitted).format("YYYY-MM-DD");
    if (!acc[dateKey]) {
      acc[dateKey] = [];
    }
    acc[dateKey].push(order);
    return acc;
  }, {});

  // Sort dates in descending order and get last 4
  const last4OrderDates = Object.keys(ordersByDate)
    .sort((a, b) => new Date(b).getTime() - new Date(a).getTime())
    .slice(0, 4);

  // Get today's date
  const today = dayjs();

  // Calculate average cases per week for each item
  Object.keys(itemStats).forEach((itemId) => {
    const stats = itemStats[itemId];

    if (supplierId === 31) {
      // For supplier_id = 31, always use all-time span (today - first date)
      if (stats.first_ordered_date) {
        const firstDate = dayjs(stats.first_ordered_date);
        const diffWeeks = Math.max(1, today.diff(firstDate, "week"));
        stats.avg_cases_per_week = Number(
          (stats.total_quantity / diffWeeks).toFixed(1)
        );
      } else {
        stats.avg_cases_per_week = 0;
      }
    } else {
      // For other suppliers, use the existing calculation method
      let totalQuantity = 0;

      // Calculate totals from last 4 order dates
      last4OrderDates.forEach((dateKey) => {
        const ordersOnDate = ordersByDate[dateKey];
        const itemOrder = ordersOnDate.find(
          (order) => order.item_id === Number(itemId)
        );
        if (itemOrder) {
          totalQuantity += Number(itemOrder.quantity);
        }
      });

      stats.avg_cases_per_week = Number((totalQuantity / 4).toFixed(1));
    }
  });

  // Update the items with their statistics
  return items.map((item) => {
    const stats = itemStats[item.id] || {
      first_ordered_date: null,
      last_ordered_date: null,
      avg_cases_per_week: 0,
      total_orders: 0,
      total_quantity: 0,
    };

    return {
      ...item,
      first_ordered_date: stats.first_ordered_date,
      last_ordered_date: stats.last_ordered_date,
      avg_cases_per_week: stats.avg_cases_per_week,
      total_orders: stats.total_orders,
      total_quantity: stats.total_quantity,
    };
  });
};

export default addLastOrderedDate;

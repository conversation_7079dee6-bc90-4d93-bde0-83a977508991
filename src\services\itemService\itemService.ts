import knex from "../../../knex/knex";
import { Item, Ordering } from "../../generated/graphql";
import { modifyUpc } from "../../util/upcs";
import { filterByUpc } from "../../util/upcs";
import addLastOrderedDate from "./addLastOrderedDate";
import getItemsBySection from "./getItemsBySection";
import modifyToCustomPricing from "./modifyToCustomPricing";
import { getHiddenProductIds } from "./customerHiddenProductsService";

// GETS
export const getItem = async (itemId: string) => {
  return knex.select("*").from("item").where("id", itemId).first();
};

export const getItems = async ({
  supplierId,
  userId,
  filters,
  pagination = { offset: 0, limit: 100 },
  sortBy = { field: "id", ordering: Ordering.Asc },
}: {
  supplierId: string;
  userId: string;
  filters?: {
    ids?: string[];
    upc?: string;
    name?: string;
    brand?: string;
    category?: string;
    section?: string;
    query?: string;
    archived?: boolean;
  };
  pagination?: { offset?: number; limit?: number };
  sortBy?: { field?: string; ordering?: string };
}): Promise<{ items: Item[]; totalCount: number }> => {
  if (!supplierId) {
    throw new Error("Supplier ID is required");
  }

  // TODO is there an cleaner way to add defaults?
  const filtersWithDefaults = {
    archived: false,
    ...filters,
  };

  const { offset, limit } = pagination;
  const { field: sortField, ordering: sortOrdering } = sortBy;
  const supplierMatch = await knex
    .select()
    .table("supplier")
    .where("id", supplierId);
  const supplierName = supplierMatch[0].name;

  if (!supplierName) {
    throw new Error("Supplier not found");
  }

  // Get hidden product IDs if userId is provided
  let hiddenProductIds: string[] = [];
  if (userId) {
    hiddenProductIds = await getHiddenProductIds(supplierId, userId);
  }

  const query = knex
    .select("*")
    .from("item")
    .where("supplier", supplierName)
    .where("local_item", false)
    .where("outdated", false)
    .where("archived", filtersWithDefaults.archived);

  // Filter out hidden products if any exist
  if (hiddenProductIds.length > 0) {
    query.whereNotIn("id", hiddenProductIds);
  }

  if (filtersWithDefaults.ids && filtersWithDefaults.ids.length > 0) {
    query.whereIn("id", filtersWithDefaults.ids);
  }

  if (filtersWithDefaults.upc) {
    query.where((whereBuilder) => {
      const modifiedUpc = modifyUpc(filtersWithDefaults.upc);
      return filterByUpc(whereBuilder, modifiedUpc);
    });
  }

  if (filtersWithDefaults.name) {
    query.where("name", "ilike", `%${filtersWithDefaults.name}%`);
  }

  // TODO: we should look into removing the upcs from the query
  if (filtersWithDefaults.query && filtersWithDefaults.query.length > 0) {
    query
      .where("name", "ilike", `%${filtersWithDefaults.query}%`)
      .orWhere("nacs_category", "ilike", `%${filtersWithDefaults.query}%`)
      .orWhere("nacs_subcategory", "ilike", `%${filtersWithDefaults.query}%`)
      .orWhere("upc1", "ilike", `%${filtersWithDefaults.query}%`)
      .orWhere("upc2", "ilike", `%${query}%`)
      .orWhere("metadata", "ilike", `%${query}%`);
  }

  if (filtersWithDefaults.brand) {
    query.where((whereBuilder) => {
      return whereBuilder
        .where("brand", "ilike", `%${filtersWithDefaults.brand}%`)
        .orWhere("parent_brand", "ilike", `%${filtersWithDefaults.brand}%`);
    });
  }

  if (filtersWithDefaults.category) {
    query.where((whereBuilder) =>
      whereBuilder
        .whereRaw(
          "? ilike ANY(string_to_array(nacs_category, '//'))",
          filtersWithDefaults.category
        )
        .orWhereRaw(
          "? ilike ANY(string_to_array(nacs_subcategory, '//'))",
          filtersWithDefaults.category
        )
    );
  }

  if (filtersWithDefaults.section && userId) {
    const section_items = await getItemsBySection(
      userId,
      filtersWithDefaults.section
    );
    query.whereIn(
      "id",
      section_items.map((item) => item.id)
    );
  }

  // Get total count
  const countQuery = query
    .clone()
    .clearSelect()
    .clearOrder()
    .count<{ count: string }>("id as count")
    .first();
  const totalCount = parseInt((await countQuery).count as string, 10);

  // Sort fields
  const allowedSortFields = [
    "id",
    "name",
    "upc1",
    "upc2",
    "upc3",
    "upc4",
    "created_at",
    "updated_at",
  ];
  if (allowedSortFields.includes(sortField)) {
    query.orderBy(`${sortField}`, sortOrdering);
  } else {
    query.orderBy("id", Ordering.Asc);
  }

  // Apply pagination
  query.offset(offset).limit(limit);

  let items = await query;

  if (userId) {
    await modifyToCustomPricing(items, userId);
    items = await addLastOrderedDate(items, userId);
  }

  return {
    items,
    totalCount,
  };
};

import knex from "../../../knex/knex";

const getOrderItemsBySupplier = async (orderId, supplier) => {
  const order = await knex
    .select("*")
    .from("order_detail")
    .where("id", orderId);
  let result = [];
  if (order[0].single_supplier && order[0].single_supplier === supplier) {
    result = await knex
      .select("*")
      .from("item")
      .join("order_item", "item.id", "order_item.item_id")
      .where("order_id", orderId)
      .orderBy("order_item.id", "desc");
  } else {
    const orderItems = await knex
      .select("*")
      .from("item")
      .join("order_item", "item.id", "order_item.item_id")
      .where("order_id", orderId)
      .where((whereBuilder) =>
        whereBuilder
          .where("item.supplier", supplier)
          .orWhere("order_item.new_supplier", supplier)
      );

    for (let i = 0; i < orderItems.length; i++) {
      const cur = orderItems[i];
      if (cur.new_supplier && cur.new_supplier !== supplier) {
        continue;
      } else {
        result.push(cur);
      }
    }
  }

  return {
    orderItems: result,
  };
};

export default getOrderItemsBySupplier;

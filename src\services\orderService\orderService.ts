import { Knex } from "knex";
import {
  Order,
  Ordering,
  OrdersFiltersV2,
  PaginationInput,
  SortBy,
} from "../../generated/graphql";
import { getUsersScheduledOnDay } from "../userService/userService";
import knex from "../../../knex/knex";
import dayjs from "../../util/dayjsConfig";
import modifyToCustomPricing from "../itemService/modifyToCustomPricing";

// Create accessors for JSON and other fields
function convertToQuery(inputField: string) {
  const [field, key] = inputField.split(".");
  if (field === "customerDetails") {
    return `"${field}" ->> '${key}'`;
  } else if (key) {
    return field + "_" + key;
  }
  return field;
}

export const getPopulatedOrders = async (orders) => {
  const orderIds = orders.map((order) => order.id);
  const orderItems = await knex
    .select("*")
    .from("item")
    .join("order_item", "item.id", "order_item.item_id")
    .whereIn("order_id", orderIds)
    .orderBy("sorting_order", "asc");
  const invoices = await knex
    .select("*")
    .from("invoice")
    .whereIn("order_id", orderIds);

  const invoiceItems = await knex
    .select("*")
    .from("invoice_item")
    .whereIn(
      "invoice_id",
      invoices.map((inv) => inv.id)
    );

  const itemsByOrderId = {};
  orderItems.forEach((item) => {
    if (!itemsByOrderId[item.order_id]) {
      itemsByOrderId[item.order_id] = [];
    }
    itemsByOrderId[item.order_id].push(item);
  });

  const invoiceItemsByInvoiceId = {};
  invoiceItems.forEach((item) => {
    if (!invoiceItemsByInvoiceId[item.invoice_id]) {
      invoiceItemsByInvoiceId[item.invoice_id] = [];
    }
    invoiceItemsByInvoiceId[item.invoice_id].push(item);
  });

  const result = Promise.all(
    orders.map(async (order) => {
      if (order.user_id && order.id in itemsByOrderId) {
        await modifyToCustomPricing(itemsByOrderId[order.id], order.user_id);
      }

      // Calculate overdue status
      let calculatedStatus = order.status;
      const invoice = invoices.find((inv) => inv.order_id === order.id);

      if (order.net_terms_days !== null && invoice) {
        const amountDue =
          Number(invoice.total || 0) - Number(invoice.paid || 0);
        if (amountDue > 0) {
          // Calculate due date: order date + net terms days
          const orderDate = dayjs(order.date_submitted);
          const dueDate = orderDate.add(order.net_terms_days, "day");
          const now = dayjs();

          if (now.isAfter(dueDate)) {
            calculatedStatus = "Overdue";
          }
        }
      }

      return {
        id: order.id,
        order_number: order.order_number,
        status: calculatedStatus,
        orderItems: itemsByOrderId[order.id] || [],
        orderName: order.order_name,
        subtotal: order.subtotal,
        discount: order.discount,
        date_submitted: order.date_submitted,
        delivery_date: order.delivery_date,
        supplier: order.single_supplier,
        supplier_logo: order.logo,
        notification_number: order.notification_number,
        notification_email: order.notification_email,
        customerDetails: order.customerDetails,
        invoice: (() => {
          const invoice = invoices.find(
            (invoice) => invoice.order_id === order.id
          );
          if (invoice) {
            return {
              ...invoice,
              invoiceItems: invoiceItemsByInvoiceId[invoice.id] || [],
            };
          }
          return null;
        })(),
        totalQuantity: (itemsByOrderId[order.id] || []).reduce(
          (totalQty, item) => totalQty + item.quantity,
          0
        ),
        notes: order.notes,
        config: order.config,
      };
    })
  );

  return result;
};

export const getOrders = async ({
  filters = { includeArchivedStores: false },
  supplierId,
  pagination = { offset: 0, limit: 50 },
  sortBy = { field: "id", ordering: Ordering.Desc },
}: {
  filters?: {
    ids?: string[];
    status?: string;
    deliveryDate?: Date;
    deliveryDateRange?: Date[];
    routeIds?: string[];
    driver?: string;
    userIds?: string[];
    userId?: string; // TODO: remove this case and only use userIds
    signed?: boolean;
    query?: string;
    lastPaidDate?: Date;
    includeArchivedStores?: boolean;
    perUserLimit?: number;
    paidStatus?: string;
  };
  supplierId: string;
  pagination?: { offset?: number; limit?: number };
  sortBy?: { field?: string; ordering?: Ordering };
}): Promise<{ orders: Order[]; totalCount: number }> => {
  if (!supplierId) {
    throw new Error("Supplier ID is required");
  }

  const sortFieldAccessor = convertToQuery(sortBy.field); // Sorting accessors for field types JSON and columns

  const filter = (queryBuilder: Knex.QueryBuilder) => {
    if (filters?.ids) {
      queryBuilder.whereIn("order_detail.id", filters.ids);
    }
    if (filters?.status) {
      switch (filters.status.toLowerCase()) {
        case "placed":
          queryBuilder.where("status", "ilike", "submitted");
          break;
        case "overdue":
          queryBuilder
            .where(function () {
              this.where("status", "ilike", "submitted").orWhere(
                "status",
                "ilike",
                "in transit"
              );
            })
            .andWhereRaw("delivery_date < CURRENT_DATE");
          break;
        case "invoiced":
          queryBuilder
            .where("status", "ilike", "in transit")
            .whereNotNull("invoice.id");
          break;
        default:
          queryBuilder.where("status", "ilike", filters.status);
          break;
      }
    }
    if (filters?.deliveryDate) {
      queryBuilder.andWhere(
        "delivery_date",
        dayjs(filters.deliveryDate).utc().format("YYYY-MM-DD")
      );
    }
    if (filters?.deliveryDateRange) {
      queryBuilder
        .andWhere(
          "delivery_date",
          ">=",
          dayjs(filters.deliveryDateRange[0]).utc().format("YYYY-MM-DD")
        )
        .andWhere(
          "delivery_date",
          "<=",
          dayjs(filters.deliveryDateRange[1]).utc().format("YYYY-MM-DD")
        );
    }
    if ((filters?.routeIds && filters?.routeIds.length) || filters?.driver) {
      queryBuilder.where((builder) => {
        if (filters?.driver) {
          // First check if there's a custom_driver on the order
          builder.where((subBuilder) => {
            subBuilder.whereRaw(
              `order_detail.config->>'custom_driver' = ?`,
              filters.driver
            );
          });
          // For orders with no custom_driver, check route_ids
          builder.orWhere((subBuilder) => {
            subBuilder
              .whereRaw(`order_detail.config->>'custom_driver' IS NULL`)
              .andWhere((routeBuilder) => {
                if (filters?.routeIds && filters?.routeIds.length) {
                  routeBuilder.where(
                    knex.raw(
                      `?::text[] && string_to_array(attain_user.route_id, ',')`,
                      [filters.routeIds]
                    )
                  );
                  routeBuilder.orWhereRaw(
                    `order_detail.config->>'custom_route' = ANY(?::text[])`,
                    [filters.routeIds]
                  );
                }
              });
          });
        } else if (filters?.routeIds && filters?.routeIds.length) {
          // If using route instead of driver, check routes
          // First check if there's a custom_route on the order
          builder.where((subBuilder) => {
            subBuilder.whereRaw(
              `order_detail.config->>'custom_route' = ANY(?::text[])`,
              [filters.routeIds]
            );
          });
          // For orders with no custom_route, check route_ids
          builder.orWhere((subBuilder) => {
            subBuilder
              .whereRaw(`order_detail.config->>'custom_route' IS NULL`)
              .andWhere((routeBuilder) => {
                if (filters?.routeIds && filters?.routeIds.length) {
                  routeBuilder.where(
                    knex.raw(
                      `?::text[] && string_to_array(attain_user.route_id, ',')`,
                      [filters.routeIds]
                    )
                  );
                }
              });
          });
        }
      });
    }
    // TODO: remove the userId case and only user userIds
    if (filters?.userIds && Array.isArray(filters.userIds)) {
      queryBuilder.whereIn("order_detail.user_id", filters.userIds);
    } else if (filters?.userId) {
      queryBuilder.andWhere("order_detail.user_id", filters.userId);
    }
    if (filters?.signed !== undefined && filters.signed !== null) {
      queryBuilder.andWhere(
        "invoice.signature_name",
        filters.signed ? "is not" : "is",
        null
      );
    }
    if (filters?.query) {
      const isNumericQuery = /^\d+$/.test(filters.query);
      queryBuilder.andWhere(function () {
        if (isNumericQuery && filters.query.length < 6) {
          this.where("order_detail.notes", "ilike", `%${filters.query}%`)
            .orWhere("order_detail.id", filters.query)
            .orWhere("invoice.id", filters.query);
        } else {
          this.where("order_detail.notes", "ilike", `%${filters.query}%`);
        }
      });
    }
    if (filters?.lastPaidDate) {
      queryBuilder.whereRaw(
        "invoice.config->>'date_last_paid' = ?",
        dayjs(filters.lastPaidDate).utc().format("YYYY-MM-DD")
      );
    }
    if (!filters?.includeArchivedStores) {
      queryBuilder.andWhere("attain_user.archived", false);
    }
    if (filters?.paidStatus) {
      switch (filters.paidStatus.toLowerCase()) {
        case "paid":
          queryBuilder.whereRaw(
            `ROUND(coalesce(invoice.total, 0)-coalesce(invoice.paid, 0), 2) <= 0`
          );
          break;
        case "unpaid":
          queryBuilder.whereRaw(
            `ROUND(coalesce(invoice.total, 0)-coalesce(invoice.paid, 0), 2) > 0`
          );
          break;
        case "partial":
          queryBuilder.whereRaw(
            `ROUND(coalesce(invoice.paid, 0), 2) > 0 AND ROUND(coalesce(invoice.total, 0)-coalesce(invoice.paid, 0), 2) > 0`
          );
          break;
        case "overdue":
          queryBuilder
            .whereRaw(
              `COALESCE(invoice.total, 0)-coalesce(invoice.paid, 0) > 0`
            )
            .whereRaw(`order_detail.net_terms_days IS NOT NULL`)
            .whereRaw(
              `CURRENT_DATE > (order_detail.date_submitted::date + INTERVAL '1 day' * order_detail.net_terms_days)`
            );
          break;
      }
    }
  };

  const supplierName = (
    await knex.select("name").from("supplier").where("id", supplierId)
  )[0].name;

  const baseQuery = knex
    .select(
      "order_status.delivery_date",
      "order_status.delivering_date",
      "order_status.submission_date",
      "order_detail.*",
      "supplier.logo",
      "invoice.id as invoice_id",
      knex.raw(
        `json_build_object('id', "attain_user".id, 'name', "attain_user".name, 'email', "attain_user".email, 'phone_number', "attain_user".phone_number, 'address', "attain_user".address, 'route_id', "attain_user".route_id, 'custom_prices', coalesce("cp".custom_prices, '[]'::json)) as "customerDetails"`
      )
    )
    .from("order_status")
    .rightOuterJoin("order_detail", "order_status.order_id", "order_detail.id")
    .leftOuterJoin("supplier", "order_detail.single_supplier", "supplier.name")
    .leftOuterJoin("attain_user", "order_detail.user_id", "attain_user.id")
    .leftOuterJoin(
      knex.raw(
        "(SELECT user_id, json_agg(json_build_object('item_id', item_id, 'price', price)) as custom_prices FROM custom_item_price GROUP BY user_id) cp USING(user_id)"
      )
    )
    .leftOuterJoin("invoice", "order_detail.id", "invoice.order_id")
    .where("single_supplier", supplierName)
    .modify(filter);

  const fullQuery = knex
    .with("orders", baseQuery)
    .select("*")
    .from("orders")
    .orderByRaw(`("orders".` + sortFieldAccessor + ") " + sortBy.ordering) // Some string munging for accessing different field types
    .limit(pagination.limit)
    .offset(pagination.offset)
    .rightJoin(
      knex.raw("(SELECT count(*) as total_count FROM orders) c ON true")
    );

  // Apply perUserLimit filter if specified
  let result;
  if (filters?.perUserLimit && filters.perUserLimit > 0) {
    // Create a window function to partition by user_id, ordering by the sort field
    const windowQuery = knex
      .with(
        "numbered_orders",
        knex.raw(`
        SELECT 
          *,
          ROW_NUMBER() OVER (
            PARTITION BY user_id 
            ORDER BY ${sortFieldAccessor} ${sortBy.ordering}
          ) as row_num
        FROM (${baseQuery.toString()}) as orders
      `)
      )
      .select("*")
      .from("numbered_orders")
      .where("row_num", "<=", filters.perUserLimit)
      .orderByRaw(`(${sortFieldAccessor}) ${sortBy.ordering}`)
      .limit(pagination.limit)
      .offset(pagination.offset)
      .rightJoin(
        knex.raw(`
          (SELECT count(*) as total_count FROM numbered_orders WHERE row_num <= ${filters.perUserLimit}) c ON true
        `)
      );
    result = await windowQuery;
  } else {
    result = await fullQuery;
  }

  const orders = await getPopulatedOrders(result.filter((order) => order.id));
  const totalCount =
    result && result.length > 0 ? parseInt(result[0]?.total_count, 10) : 0;

  return { orders, totalCount };
};

export const getExpectedOrders = async (
  supplierId: string,
  date: Date
): Promise<Order[]> => {
  const selectedDate = date ?? new Date();
  const usersOnScheduledDay = await getUsersScheduledOnDay(
    supplierId,
    selectedDate
  );
  const { orders } = await getOrders({
    supplierId,
    filters: {
      deliveryDate: selectedDate,
      userIds: usersOnScheduledDay.map((user) => user.id),
    },
    pagination: { offset: 0, limit: 500 },
  });
  const expectedOrders = usersOnScheduledDay
    .filter(
      (user) => !orders.some((order) => order.customerDetails?.id === user.id)
    )
    .map((user) => ({
      id: user.id,
      customerDetails: user,
      status: "Expected",
      delivery_date: dayjs(selectedDate).toDate(),
      subtotal: 0,
    }));
  return expectedOrders;
};

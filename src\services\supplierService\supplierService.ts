import knex from "../../../knex/knex";
import { CreateSupplierInput } from "../../generated/graphql";

export const getSupplierConfig = async (supplierId: string) => {
  const configs = await knex("supplier_config")
    .where("supplier_id", supplierId)
    .select("key", "value");

  const result = configs.reduce((acc, config) => {
    try {
      // Attempt to parse as JSON, fall back to raw value if not valid JSON
      acc[config.key] = JSON.parse(config.value);
    } catch (e) {
      acc[config.key] = config.value;
    }
    return acc;
  }, {});

  return result;
};

export const createSupplierConfig = async (
  supplierId: string,
  key: string,
  value: any
) => {
  const stringValue = typeof value === "string" ? value : JSON.stringify(value);

  const [created] = await knex("supplier_config")
    .insert({
      supplier_id: supplierId,
      key,
      value: stringValue,
    })
    .returning("*");

  return getSupplierConfig(supplierId);
};

export const updateSupplierConfig = async (
  supplierId: string,
  key: string,
  value: any
) => {
  if (value === null) {
    // If value is null, delete the configuration entry
    const deletedCount = await knex("supplier_config")
      .where({
        supplier_id: supplierId,
        key,
      })
      .del();
    return getSupplierConfig(supplierId);
  } else {
    // Otherwise, proceed with the update logic
    const stringValue =
      typeof value === "string" ? value : JSON.stringify(value);

    const [updated] = await knex("supplier_config")
      .where({
        supplier_id: supplierId,
        key,
      })
      .update({
        value: stringValue,
        updated_at: knex.fn.now(),
      })
      .returning("*");

    return getSupplierConfig(supplierId);
  }
};

export const upsertSupplierConfig = async (
  supplierId: string,
  key: string,
  value: any
) => {
  // First check if the config exists
  const existing = await knex("supplier_config")
    .where({
      supplier_id: supplierId,
      key,
    })
    .first();

  if (existing) {
    // Update existing config
    return await updateSupplierConfig(supplierId, key, value);
  } else {
    // Create new config
    return await createSupplierConfig(supplierId, key, value);
  }
};

const getSupplier = async (supplierId: string) => {
  const supplier = await knex("supplier").where("id", supplierId).first();
  return supplier;
};

const createSupplier = async (supplier: CreateSupplierInput) => {
  const {
    password, // We'll ignore the password for nowsince we're removing auth
    name,
    email,
    ...supplierData
  } = supplier;

  try {
    const [createdSupplier] = await knex("supplier")
      .insert({
        ...supplierData,
        name,
        email,
      })
      .returning("*");

    return createdSupplier;
  } catch (error) {
    console.error("Error creating supplier:", error);
    throw new Error(`Error creating supplier: ${error.message}`);
  }
};

export { createSupplier, getSupplier };
